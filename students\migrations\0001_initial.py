# Generated by Django 5.2.1 on 2025-05-22 08:50

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(max_length=10, unique=True, validators=[django.core.validators.RegexValidator('^\\d{10}$', 'Mã sinh viên phải có 10 chữ số.')], verbose_name='Mã sinh viên')),
                ('first_name', models.CharField(max_length=50, verbose_name='Tên')),
                ('last_name', models.CharField(max_length=50, verbose_name='Họ')),
                ('date_of_birth', models.DateField(verbose_name='<PERSON><PERSON><PERSON> sinh')),
                ('gender', models.Char<PERSON>ield(choices=[('M', 'Nam'), ('F', 'Nữ'), ('O', 'Khác')], max_length=1, verbose_name='Giới tính')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('phone_number', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'Số điện thoại không hợp lệ.')], verbose_name='Số điện thoại')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Địa chỉ')),
                ('enrollment_date', models.DateField(auto_now_add=True, verbose_name='Ngày nhập học')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='student_photos/', verbose_name='Ảnh')),
                ('is_active', models.BooleanField(default=True, verbose_name='Đang học')),
            ],
            options={
                'verbose_name': 'Sinh viên',
                'verbose_name_plural': 'Sinh viên',
                'ordering': ['last_name', 'first_name'],
            },
        ),
    ]
