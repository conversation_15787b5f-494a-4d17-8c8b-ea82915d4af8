{% extends 'base.html' %}

{% block title %}
    {% if form.instance.pk %}Cậ<PERSON> nhật{% else %}Thêm mới{% endif %} Đ<PERSON>ng ký - Hệ thống Quản lý Sinh viên
{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'enrollment-list' %}">Đăng ký môn học</a></li>
            <li class="breadcrumb-item active">
                {% if form.instance.pk %}Cập nhật{% else %}Thêm mới{% endif %} Đ<PERSON>ng ký
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-{% if form.instance.pk %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if form.instance.pk %}Cập nhật{% else %}Thêm mới{% endif %} Đăng ký
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Lỗi:</strong> Vui lòng kiểm tra lại thông tin.
                            {% if form.non_field_errors %}
                            <ul class="mb-0 mt-2">
                                {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        {% if form.instance.pk %}
                        <!-- Cập nhật điểm -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Sinh viên</label>
                                    <input type="text" class="form-control" value="{{ form.instance.student.full_name }} ({{ form.instance.student.student_id }})" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Môn học</label>
                                    <input type="text" class="form-control" value="{{ form.instance.course.name }} ({{ form.instance.course.course_code }})" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.grade.id_for_label }}" class="form-label">{{ form.grade.label }}</label>
                                    <select name="{{ form.grade.name }}" id="{{ form.grade.id_for_label }}" 
                                            class="form-select {% if form.grade.errors %}is-invalid{% endif %}">
                                        <option value="" {% if not form.grade.value %}selected{% endif %}>-- Chọn điểm chữ --</option>
                                        {% for choice in form.grade.field.choices %}
                                        <option value="{{ choice.0 }}" {% if form.grade.value == choice.0 %}selected{% endif %}>
                                            {{ choice.1 }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    {% if form.grade.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.grade.errors %}{{ error }}{% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.numeric_grade.id_for_label }}" class="form-label">{{ form.numeric_grade.label }}</label>
                                    <input type="number" name="{{ form.numeric_grade.name }}" id="{{ form.numeric_grade.id_for_label }}" 
                                           class="form-control {% if form.numeric_grade.errors %}is-invalid{% endif %}" 
                                           value="{{ form.numeric_grade.value|default:'' }}" step="0.1" min="0" max="10">
                                    {% if form.numeric_grade.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.numeric_grade.errors %}{{ error }}{% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        {% else %}
                        <!-- Thêm mới đăng ký -->
                        <div class="mb-4">
                            <label for="{{ form.student.id_for_label }}" class="form-label">{{ form.student.label }}</label>
                            <select name="{{ form.student.name }}" id="{{ form.student.id_for_label }}" 
                                    class="form-select {% if form.student.errors %}is-invalid{% endif %}" required>
                                <option value="" {% if not form.student.value %}selected{% endif %}>-- Chọn sinh viên --</option>
                                {% for choice in form.student.field.choices %}
                                {% if choice.0 %}
                                <option value="{{ choice.0 }}" {% if form.student.value == choice.0|stringformat:"s" or request.GET.student_id == choice.0|stringformat:"s" %}selected{% endif %}>
                                    {{ choice.1 }}
                                </option>
                                {% endif %}
                                {% endfor %}
                            </select>
                            {% if form.student.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.student.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="{{ form.course.id_for_label }}" class="form-label">{{ form.course.label }}</label>
                            <select name="{{ form.course.name }}" id="{{ form.course.id_for_label }}" 
                                    class="form-select {% if form.course.errors %}is-invalid{% endif %}" required>
                                <option value="" {% if not form.course.value %}selected{% endif %}>-- Chọn môn học --</option>
                                {% for choice in form.course.field.choices %}
                                {% if choice.0 %}
                                <option value="{{ choice.0 }}" {% if form.course.value == choice.0|stringformat:"s" or request.GET.course_id == choice.0|stringformat:"s" %}selected{% endif %}>
                                    {{ choice.1 }}
                                </option>
                                {% endif %}
                                {% endfor %}
                            </select>
                            {% if form.course.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.course.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'enrollment-list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Lưu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
