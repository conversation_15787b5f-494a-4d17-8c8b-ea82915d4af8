{% extends 'base.html' %}

{% block title %}Thông tin cá nhân - {{ student.full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Thông tin cá nhân</h5>
                </div>
                <div class="card-body text-center">
                    {% if student.photo %}
                        <img src="{{ student.photo.url }}" alt="Ảnh sinh viên" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                        <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                            <i class="fas fa-user fa-4x text-white"></i>
                        </div>
                    {% endif %}
                    <h4>{{ student.full_name }}</h4>
                    <p class="text-muted">{{ student.student_id }}</p>
                    <span class="badge bg-{% if student.is_active %}success{% else %}danger{% endif %}">
                        {% if student.is_active %}Đang học{% else %}Tạm nghỉ{% endif %}
                    </span>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Chi tiết thông tin</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Mã sinh viên:</strong></td>
                                    <td>{{ student.student_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Họ và tên:</strong></td>
                                    <td>{{ student.full_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày sinh:</strong></td>
                                    <td>{{ student.date_of_birth|date:"d/m/Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Giới tính:</strong></td>
                                    <td>{{ student.get_gender_display }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ student.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td>{{ student.phone_number|default:"Chưa cập nhật" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Địa chỉ:</strong></td>
                                    <td>{{ student.address|default:"Chưa cập nhật" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày nhập học:</strong></td>
                                    <td>{{ student.enrollment_date|date:"d/m/Y" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Thống kê học tập</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <a href="{% url 'my-courses-all' %}" class="text-decoration-none">
                                <div class="border rounded p-3 clickable-card">
                                    <h3 class="text-primary">{{ enrollments.count }}</h3>
                                    <p class="mb-0">Tổng môn học</p>
                                    <small class="text-muted">
                                        <i class="fas fa-arrow-right me-1"></i>Xem chi tiết
                                    </small>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'my-courses-all' %}" class="text-decoration-none">
                                <div class="border rounded p-3 clickable-card">
                                    <h3 class="text-success">{{ enrollments|length }}</h3>
                                    <p class="mb-0">Đã đăng ký</p>
                                    <small class="text-muted">
                                        <i class="fas fa-arrow-right me-1"></i>Xem chi tiết
                                    </small>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'my-courses-graded' %}" class="text-decoration-none">
                                <div class="border rounded p-3 clickable-card">
                                    {% with graded_count=0 %}
                                        {% for enrollment in enrollments %}
                                            {% if enrollment.grade %}
                                                {% with graded_count=graded_count|add:1 %}{% endwith %}
                                            {% endif %}
                                        {% endfor %}
                                        <h3 class="text-info">{{ graded_count }}</h3>
                                    {% endwith %}
                                    <p class="mb-0">Có điểm</p>
                                    <small class="text-muted">
                                        <i class="fas fa-arrow-right me-1"></i>Xem chi tiết
                                    </small>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <a href="{% url 'my-courses' %}" class="btn btn-outline-success">
                                <i class="fas fa-book-open me-1"></i>Xem tất cả môn học của tôi
                            </a>
                            <a href="{% url 'my-enrollment-management' %}" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-cog me-1"></i>Quản lý đăng ký
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-book-open me-2"></i>Môn học đã đăng ký</h5>
                </div>
                <div class="card-body">
                    {% if enrollments %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Mã môn học</th>
                                        <th>Tên môn học</th>
                                        <th>Tín chỉ</th>
                                        <th>Ngày đăng ký</th>
                                        <th>Điểm</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for enrollment in enrollments %}
                                    <tr>
                                        <td>{{ enrollment.course.course_code }}</td>
                                        <td>{{ enrollment.course.name }}</td>
                                        <td>{{ enrollment.course.credits }}</td>
                                        <td>{{ enrollment.enrollment_date|date:"d/m/Y" }}</td>
                                        <td>
                                            {% if enrollment.grade %}
                                                <span class="badge bg-primary">{{ enrollment.grade }}</span>
                                                {% if enrollment.numeric_grade %}
                                                    ({{ enrollment.numeric_grade }})
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Chưa có điểm</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if enrollment.course.is_active %}
                                                <span class="badge bg-success">Đang mở</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Đã đóng</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Bạn chưa đăng ký môn học nào.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
