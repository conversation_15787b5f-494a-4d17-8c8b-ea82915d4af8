{% extends 'base.html' %}

{% block title %}M<PERSON>n học của tôi - {{ student.full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-book-open me-2"></i>Môn học của tôi</h1>
        <div class="text-muted">
            <i class="fas fa-user me-1"></i>{{ student.full_name }} ({{ student.student_id }})
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <a href="{% url 'my-courses-all' %}" class="text-decoration-none">
                <div class="card bg-primary text-white clickable-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>{{ total_courses }}</h4>
                                <p class="mb-0">Tổng môn học</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-book fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-primary border-0">
                        <small class="text-white-50">
                            <i class="fas fa-arrow-right me-1"></i>Nhấn để xem chi tiết
                        </small>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-3">
            <a href="{% url 'my-courses-graded' %}" class="text-decoration-none">
                <div class="card bg-success text-white clickable-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>{{ graded_courses }}</h4>
                                <p class="mb-0">Đã có điểm</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-success border-0">
                        <small class="text-white-50">
                            <i class="fas fa-arrow-right me-1"></i>Nhấn để xem chi tiết
                        </small>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{% if avg_grade %}{{ avg_grade|floatformat:1 }}{% else %}--{% endif %}</h4>
                            <p class="mb-0">Điểm trung bình</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-info border-0">
                    <small class="text-white-50">
                        <i class="fas fa-info-circle me-1"></i>Điểm trung bình học kỳ
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <a href="{% url 'my-courses-ungraded' %}" class="text-decoration-none">
                <div class="card bg-warning text-dark clickable-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>{{ total_courses|add:"-"|add:graded_courses }}</h4>
                                <p class="mb-0">Chưa có điểm</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-warning border-0">
                        <small class="text-dark-50">
                            <i class="fas fa-arrow-right me-1"></i>Nhấn để xem chi tiết
                        </small>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Thao tác nhanh</h5>
            <div class="btn-group">
                <a href="{% url 'my-enrollment-management' %}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i>Quản lý đăng ký
                </a>
                <a href="{% url 'my-courses-all' %}" class="btn btn-outline-info">
                    <i class="fas fa-list me-1"></i>Xem tất cả môn học
                </a>
            </div>
        </div>
    </div>

    <!-- Courses List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Danh sách môn học</h5>
                <div class="btn-group btn-group-sm">
                    <a href="{% url 'my-courses-all' %}" class="btn btn-outline-primary">Xem tất cả</a>
                    <a href="{% url 'my-courses-graded' %}" class="btn btn-outline-success">Đã có điểm</a>
                    <a href="{% url 'my-courses-ungraded' %}" class="btn btn-outline-warning">Chưa có điểm</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if enrollments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>STT</th>
                                <th>Mã môn học</th>
                                <th>Tên môn học</th>
                                <th>Tín chỉ</th>
                                <th>Học kỳ</th>
                                <th>Năm học</th>
                                <th>Ngày đăng ký</th>
                                <th>Điểm chữ</th>
                                <th>Điểm số</th>
                                <th>Trạng thái</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for enrollment in enrollments %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td><strong>{{ enrollment.course.course_code }}</strong></td>
                                <td>{{ enrollment.course.name }}</td>
                                <td class="text-center">{{ enrollment.course.credits }}</td>
                                <td class="text-center">{{ enrollment.course.semester }}</td>
                                <td class="text-center">{{ enrollment.course.year }}</td>
                                <td>{{ enrollment.enrollment_date|date:"d/m/Y" }}</td>
                                <td class="text-center">
                                    {% if enrollment.grade %}
                                        {% if enrollment.grade == 'A' %}
                                            <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'B' %}
                                            <span class="badge bg-primary">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'C' %}
                                            <span class="badge bg-warning">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'D' %}
                                            <span class="badge bg-danger">{{ enrollment.grade }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ enrollment.grade }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">--</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if enrollment.numeric_grade %}
                                        {% if enrollment.numeric_grade >= 8.5 %}
                                            <span class="text-success fw-bold">{{ enrollment.numeric_grade }}</span>
                                        {% elif enrollment.numeric_grade >= 7.0 %}
                                            <span class="text-primary fw-bold">{{ enrollment.numeric_grade }}</span>
                                        {% elif enrollment.numeric_grade >= 5.5 %}
                                            <span class="text-warning fw-bold">{{ enrollment.numeric_grade }}</span>
                                        {% else %}
                                            <span class="text-danger fw-bold">{{ enrollment.numeric_grade }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">--</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if enrollment.course.is_active %}
                                        <span class="badge bg-success">Đang mở</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Đã đóng</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Grade Legend -->
                <div class="mt-3">
                    <h6>Thang điểm:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-success">A: 8.5-10</span>
                        <span class="badge bg-primary">B: 7.0-8.4</span>
                        <span class="badge bg-warning">C: 5.5-6.9</span>
                        <span class="badge bg-danger">D: 4.0-5.4</span>
                        <span class="badge bg-secondary">F: < 4.0</span>
                    </div>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-book fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Chưa có môn học nào</h4>
                    <p class="text-muted">Bạn chưa đăng ký môn học nào. Vui lòng liên hệ với quản trị viên để đăng ký môn học.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
