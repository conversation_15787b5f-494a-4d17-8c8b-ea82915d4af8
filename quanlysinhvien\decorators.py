from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse


def admin_required(view_func):
    """
    Decorator để yêu cầu user phải là admin (is_staff=True).
    Tự động bao gồm login_required.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_staff:
            messages.error(request, 'Bạn không có quyền truy cập vào trang này. Chỉ quản trị viên mới có thể thực hiện chức năng này.')
            return redirect('home')
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def staff_required(view_func):
    """
    Decorator tương tự admin_required, dùng để kiểm tra is_staff.
    """
    return admin_required(view_func)
