{% extends 'base.html' %}
{% load static %}

{% block title %}Thống kê Chatbot - <PERSON><PERSON> thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar me-2"></i>Thống kê Chatbot</h2>
                <a href="{% url 'admin:index' %}" class="btn btn-secondary">
                    <i class="fas fa-cog me-1"></i>Quản lý Admin
                </a>
            </div>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
    </div>
    {% else %}
    
    <!-- Thống kê tổng quan -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_sessions }}</h4>
                            <p class="mb-0">Tổng phiên chat</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-comments fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ active_sessions }}</h4>
                            <p class="mb-0">Phiên đang hoạt động</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_messages }}</h4>
                            <p class="mb-0">Tổng tin nhắn</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_faqs }}</h4>
                            <p class="mb-0">FAQ đang hoạt động</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-question-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê 7 ngày qua -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-week me-2"></i>Hoạt động 7 ngày qua</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-primary">{{ recent_sessions }}</h3>
                                <p class="mb-0">Phiên chat mới</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-success">{{ recent_messages }}</h3>
                                <p class="mb-0">Tin nhắn</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-info">{{ active_users }}</h3>
                                <p class="mb-0">Người dùng hoạt động</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-warning">{{ avg_response_time }}s</h3>
                                <p class="mb-0">Thời gian phản hồi TB</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bullseye me-2"></i>Intent phổ biến (7 ngày qua)</h5>
                </div>
                <div class="card-body">
                    {% if popular_intents %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Intent</th>
                                    <th>Số lượng</th>
                                    <th>%</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for intent in popular_intents %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ intent.intent }}</span>
                                    </td>
                                    <td>{{ intent.count }}</td>
                                    <td>
                                        {% widthratio intent.count recent_messages 100 %}%
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">Chưa có dữ liệu intent</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Liên kết quản lý -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools me-2"></i>Quản lý Chatbot</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'admin:chatbot_faq_changelist' %}" class="btn btn-outline-primary btn-block mb-2">
                                <i class="fas fa-question-circle me-2"></i>Quản lý FAQ
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'admin:chatbot_chathistory_changelist' %}" class="btn btn-outline-info btn-block mb-2">
                                <i class="fas fa-history me-2"></i>Lịch sử Chat
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'admin:chatbot_chatsession_changelist' %}" class="btn btn-outline-success btn-block mb-2">
                                <i class="fas fa-users me-2"></i>Phiên Chat
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'chatbot:chat' %}" class="btn btn-outline-warning btn-block mb-2">
                                <i class="fas fa-robot me-2"></i>Test Chatbot
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% endif %}
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-block {
    width: 100%;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
