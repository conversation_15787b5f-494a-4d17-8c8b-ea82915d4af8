"""
Training data cho chatbot - <PERSON><PERSON><PERSON> cách hỏi khác nhau cho mỗi intent
"""

TRAINING_EXAMPLES = {
    'greeting': [
        "<PERSON>n chào",
        "<PERSON><PERSON><PERSON> bạn", 
        "Hello",
        "Hi",
        "Hey",
        "<PERSON><PERSON><PERSON> em",
        "<PERSON><PERSON><PERSON> anh",
        "Chào chị",
        "<PERSON><PERSON> lô",
        "<PERSON>o",
        "Good morning",
        "Chào buổi sáng",
        "Chào buổi chiều",
        "Chào buổi tối",
        "Xin chào bot",
        "Chào chatbot",
        "Chào trợ lý ảo"
    ],
    
    'student_info': [
        "Thông tin sinh viên của tôi",
        "Xem thông tin cá nhân",
        "<PERSON><PERSON> sơ của tôi",
        "Profile sinh viên",
        "Thông tin về tôi",
        "Tôi là ai",
        "M<PERSON> sinh viên của tôi",
        "Tên tôi là gì",
        "Thông tin học sinh",
        "<PERSON><PERSON> sơ cá nhân",
        "<PERSON> tiết sinh viên",
        "<PERSON><PERSON> liệu cá nhân",
        "Thông tin cơ bản",
        "Xem profile",
        "Info của tôi",
        "Thông tin bản thân",
        "Hồ sơ học tập",
        "Tôi là sinh viên nào",
        "Cho tôi xem thông tin",
        "Hiển thị thông tin sinh viên"
    ],
    
    'course_info': [
        "Thông tin môn học",
        "Danh sách môn học", 
        "Môn học nào có",
        "Xem môn học",
        "Tìm môn học",
        "Tra cứu môn học",
        "Môn IT001",
        "Course information",
        "Subject info",
        "Học phần",
        "Lớp học",
        "Khóa học",
        "Môn gì có",
        "Có môn nào",
        "Thông tin course",
        "Chi tiết môn học",
        "Mô tả môn học",
        "Nội dung môn học",
        "Tín chỉ môn học",
        "Lịch học môn"
    ],
    
    'enrollment': [
        "Danh sách môn đã đăng ký",
        "Môn học của tôi",
        "My courses",
        "Tôi đăng ký môn gì",
        "Môn đã đăng ký",
        "Các môn tôi học",
        "Môn tôi đăng ký",
        "Lịch sử đăng ký",
        "Xem đăng ký",
        "Môn của tôi",
        "Danh sách đăng ký",
        "Tôi có môn nào",
        "Môn nào tôi đăng ký",
        "Course list của tôi",
        "Học phần đã đăng ký",
        "Môn tôi học",
        "Đăng ký học",
        "Registration của tôi",
        "Enroll courses",
        "Subjects của tôi"
    ],
    
    'grade': [
        "Điểm số của tôi",
        "Xem điểm",
        "Điểm tôi",
        "Kết quả học tập",
        "Điểm trung bình",
        "GPA của tôi",
        "Bảng điểm",
        "Transcript",
        "Tôi được bao nhiêu điểm",
        "Điểm các môn",
        "Kết quả thi",
        "Điểm kiểm tra",
        "Điểm cuối kỳ",
        "Điểm giữa kỳ",
        "Điểm tổng kết",
        "Thành tích học tập",
        "Kết quả học tập của tôi",
        "Điểm từng môn",
        "Grade của tôi",
        "Score của tôi",
        "Mark của tôi",
        "Academic result",
        "Tra cứu điểm",
        "Điểm học tập"
    ],
    
    'help': [
        "Help",
        "Giúp đỡ",
        "Hướng dẫn",
        "Trợ giúp",
        "Hỗ trợ",
        "Làm thế nào",
        "How to",
        "Cách",
        "Hướng dẫn sử dụng",
        "Giúp tôi",
        "Support",
        "Assistance",
        "Guide",
        "Tôi cần giúp",
        "Không biết làm sao",
        "Chỉ dẫn",
        "Hướng dẫn chi tiết",
        "Cách sử dụng",
        "Làm gì",
        "Tôi có thể hỏi gì"
    ],
    
    'goodbye': [
        "Tạm biệt",
        "Bye",
        "Goodbye",
        "See you",
        "Chào tạm biệt",
        "Hẹn gặp lại",
        "Kết thúc",
        "Exit",
        "Quit",
        "Chào nhé",
        "Bye bye",
        "See you later",
        "Farewell",
        "Tạm biệt bot",
        "Chào chatbot",
        "Kết thúc cuộc trò chuyện",
        "Dừng chat",
        "Thoát",
        "End"
    ]
}

# Test cases với các câu hỏi phức tạp hơn
COMPLEX_TEST_CASES = [
    # Câu hỏi mơ hồ
    ("tôi muốn xem", "student_info"),  # Có thể là xem thông tin
    ("cho tôi biết", "help"),  # Cần clarification
    ("tôi có gì", "student_info"),  # Có thể là thông tin cá nhân
    
    # Câu hỏi kết hợp
    ("thông tin và điểm của tôi", "student_info"),  # Ưu tiên student_info
    ("môn học và điểm", "course_info"),  # Ưu tiên course_info
    ("đăng ký và kết quả", "enrollment"),  # Ưu tiên enrollment
    
    # Câu hỏi với typo
    ("thong tin sinh vien", "student_info"),
    ("diem so cua toi", "grade"),
    ("mon hoc da dang ky", "enrollment"),
    
    # Câu hỏi dài
    ("Tôi muốn xem thông tin chi tiết về sinh viên của mình", "student_info"),
    ("Cho tôi biết danh sách tất cả các môn học mà tôi đã đăng ký", "enrollment"),
    ("Tôi cần kiểm tra điểm số và kết quả học tập của mình", "grade"),
    
    # Câu hỏi với context
    ("của tôi", "general"),  # Cần context
    ("xem thêm", "general"),  # Cần context
    ("chi tiết", "general"),  # Cần context
    
    # Câu hỏi về hệ thống
    ("hệ thống này làm gì", "help"),
    ("tính năng gì", "help"),
    ("có thể hỏi gì", "help"),
    
    # Câu hỏi phủ định
    ("tôi không biết hỏi gì", "help"),
    ("không hiểu", "help"),
    ("chưa rõ", "help"),
]

def get_training_examples_for_intent(intent: str):
    """Lấy training examples cho một intent cụ thể"""
    return TRAINING_EXAMPLES.get(intent, [])

def get_all_training_data():
    """Lấy tất cả training data"""
    return TRAINING_EXAMPLES

def get_test_cases():
    """Lấy test cases phức tạp"""
    return COMPLEX_TEST_CASES
