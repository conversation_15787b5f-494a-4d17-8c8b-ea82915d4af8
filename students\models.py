from django.db import models
from django.core.validators import RegexValidator

class Student(models.Model):
    GENDER_CHOICES = [
        ('M', 'Nam'),
        ('F', 'Nữ'),
        ('O', '<PERSON>h<PERSON>c'),
    ]

    student_id = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(r'^[A-Za-z0-9]+$', '<PERSON><PERSON> sinh viên chỉ được chứa chữ cái và số, không có ký tự đặc biệt.')],
        verbose_name="Mã sinh viên"
    )
    first_name = models.CharField(max_length=50, verbose_name="Tên")
    last_name = models.CharField(max_length=50, verbose_name="<PERSON><PERSON>")
    date_of_birth = models.DateField(verbose_name="<PERSON><PERSON><PERSON> sinh")
    gender = models.Char<PERSON><PERSON>(max_length=1, choices=GENDER_CHOICES, verbose_name="<PERSON><PERSON><PERSON><PERSON> t<PERSON>")
    email = models.EmailField(unique=True, verbose_name="Email")
    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'Số điện thoại không hợp lệ.')],
        verbose_name="Số điện thoại"
    )
    address = models.TextField(blank=True, null=True, verbose_name="Địa chỉ")
    enrollment_date = models.DateField(auto_now_add=True, verbose_name="Ngày nhập học")
    photo = models.ImageField(upload_to='student_photos/', blank=True, null=True, verbose_name="Ảnh")
    is_active = models.BooleanField(default=True, verbose_name="Đang học")

    class Meta:
        verbose_name = "Sinh viên"
        verbose_name_plural = "Sinh viên"
        ordering = ['last_name', 'first_name']

    def __str__(self):
        return f"{self.last_name} {self.first_name} ({self.student_id})"

    def full_name(self):
        return f"{self.last_name} {self.first_name}"
    full_name.short_description = "Họ và tên"
