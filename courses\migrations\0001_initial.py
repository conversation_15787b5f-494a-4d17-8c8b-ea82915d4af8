# Generated by Django 5.2.1 on 2025-05-22 08:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('course_code', models.CharField(max_length=10, unique=True, verbose_name='Mã môn học')),
                ('name', models.CharField(max_length=100, verbose_name='Tên môn học')),
                ('description', models.TextField(blank=True, null=True, verbose_name='<PERSON><PERSON> tả')),
                ('credits', models.PositiveSmallIntegerField(verbose_name='Số tín chỉ')),
                ('semester', models.CharField(choices=[('1', '<PERSON><PERSON><PERSON> kỳ 1'), ('2', '<PERSON><PERSON><PERSON> kỳ 2'), ('3', '<PERSON><PERSON><PERSON> kỳ hè')], max_length=1, verbose_name='<PERSON><PERSON><PERSON> kỳ')),
                ('year', models.PositiveSmallIntegerField(verbose_name='Năm học')),
                ('start_date', models.DateField(verbose_name='Ngày bắt đầu')),
                ('end_date', models.DateField(verbose_name='Ngày kết thúc')),
                ('is_active', models.BooleanField(default=True, verbose_name='Đang mở')),
            ],
            options={
                'verbose_name': 'Môn học',
                'verbose_name_plural': 'Môn học',
                'ordering': ['year', 'semester', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrollment_date', models.DateField(auto_now_add=True, verbose_name='Ngày đăng ký')),
                ('grade', models.CharField(blank=True, choices=[('A+', 'A+'), ('A', 'A'), ('B+', 'B+'), ('B', 'B'), ('C+', 'C+'), ('C', 'C'), ('D+', 'D+'), ('D', 'D'), ('F', 'F')], max_length=2, null=True, verbose_name='Điểm chữ')),
                ('numeric_grade', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Điểm số')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='courses.course', verbose_name='Môn học')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='students.student', verbose_name='Sinh viên')),
            ],
            options={
                'verbose_name': 'Đăng ký môn học',
                'verbose_name_plural': 'Đăng ký môn học',
                'ordering': ['-enrollment_date'],
                'unique_together': {('student', 'course')},
            },
        ),
    ]
