from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User, Group
from django.core.exceptions import ValidationError

class CustomUserCreationForm(UserCreationForm):
    ROLE_CHOICES = [
        ('admin', 'Quản trị viên'),
        ('student', 'Sinh viên'),
    ]
    
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nhập email của bạn'
        }),
        label='Email'
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nhập tên của bạn'
        }),
        label='Tên'
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '<PERSON><PERSON>ậ<PERSON> họ của bạn'
        }),
        label='Họ'
    )
    
    role = forms.ChoiceField(
        choices=ROLE_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='Vai trò'
    )
    
    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2', 'role')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize form fields
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Nhập tên đăng nhập'
        })
        self.fields['username'].label = 'Tên đăng nhập'
        self.fields['username'].help_text = 'Tên đăng nhập phải có ít nhất 3 ký tự và chỉ chứa chữ cái, số và @/./+/-/_'
        
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Nhập mật khẩu'
        })
        self.fields['password1'].label = 'Mật khẩu'
        
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Nhập lại mật khẩu'
        })
        self.fields['password2'].label = 'Xác nhận mật khẩu'
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError('Email này đã được sử dụng.')
        return email
    
    def clean_username(self):
        username = self.cleaned_data.get('username')
        if len(username) < 3:
            raise ValidationError('Tên đăng nhập phải có ít nhất 3 ký tự.')
        if User.objects.filter(username=username).exists():
            raise ValidationError('Tên đăng nhập này đã được sử dụng.')
        return username
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        
        if commit:
            user.save()
            
            # Assign role based on selection
            role = self.cleaned_data['role']
            if role == 'admin':
                user.is_staff = True
                user.is_superuser = True
                user.save()
                
                # Add to admin group if exists
                admin_group, created = Group.objects.get_or_create(name='Administrators')
                user.groups.add(admin_group)
            elif role == 'student':
                user.is_staff = False
                user.is_superuser = False
                user.save()
                
                # Add to student group if exists
                student_group, created = Group.objects.get_or_create(name='Students')
                user.groups.add(student_group)
        
        return user
