{% extends 'base.html' %}

{% block title %}<PERSON><PERSON>ng ký Môn học - <PERSON>ệ thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-clipboard-list me-2"></i>Đăng ký Môn học</h1>
        {% if user.is_staff %}
        <a href="{% url 'enrollment-create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Thêm Đăng ký
        </a>
        {% endif %}
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-5">
                    <label for="student_id" class="form-label">Sinh viên</label>
                    <select name="student_id" id="student_id" class="form-select">
                        <option value="">-- T<PERSON>t cả sinh viên --</option>
                        {% for student in view.get_students %}
                        <option value="{{ student.id }}" {% if request.GET.student_id == student.id|stringformat:"s" %}selected{% endif %}>
                            {{ student.full_name }} ({{ student.student_id }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="course_id" class="form-label">Môn học</label>
                    <select name="course_id" id="course_id" class="form-select">
                        <option value="">-- Tất cả môn học --</option>
                        {% for course in view.get_courses %}
                        <option value="{{ course.id }}" {% if request.GET.course_id == course.id|stringformat:"s" %}selected{% endif %}>
                            {{ course.name }} ({{ course.course_code }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Lọc</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Enrollment List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Danh sách Đăng ký</h5>
        </div>
        <div class="card-body">
            {% if enrollments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Sinh viên</th>
                            <th>Môn học</th>
                            <th>Học kỳ</th>
                            <th>Năm học</th>
                            <th>Ngày đăng ký</th>
                            <th>Điểm</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for enrollment in enrollments %}
                        <tr>
                            <td>
                                <a href="{% url 'student-detail' enrollment.student.id %}">
                                    {{ enrollment.student.full_name }}
                                </a>
                                <small class="d-block text-muted">{{ enrollment.student.student_id }}</small>
                            </td>
                            <td>
                                <a href="{% url 'course-detail' enrollment.course.id %}">
                                    {{ enrollment.course.name }}
                                </a>
                                <small class="d-block text-muted">{{ enrollment.course.course_code }}</small>
                            </td>
                            <td>{{ enrollment.course.get_semester_display }}</td>
                            <td>{{ enrollment.course.year }}</td>
                            <td>{{ enrollment.enrollment_date }}</td>
                            <td>
                                {% if enrollment.grade %}
                                <span class="badge bg-success">{{ enrollment.grade }}</span>
                                {% if enrollment.numeric_grade %}
                                <small class="text-muted">({{ enrollment.numeric_grade }})</small>
                                {% endif %}
                                {% else %}
                                <span class="badge bg-secondary">Chưa có điểm</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_staff %}
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'enrollment-update' enrollment.id %}" class="btn btn-warning" title="Cập nhật điểm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'enrollment-delete' enrollment.id %}" class="btn btn-danger" title="Hủy đăng ký">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.student_id %}&student_id={{ request.GET.student_id }}{% endif %}{% if request.GET.course_id %}&course_id={{ request.GET.course_id }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.student_id %}&student_id={{ request.GET.student_id }}{% endif %}{% if request.GET.course_id %}&course_id={{ request.GET.course_id }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.student_id %}&student_id={{ request.GET.student_id }}{% endif %}{% if request.GET.course_id %}&course_id={{ request.GET.course_id }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.student_id %}&student_id={{ request.GET.student_id }}{% endif %}{% if request.GET.course_id %}&course_id={{ request.GET.course_id }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.student_id %}&student_id={{ request.GET.student_id }}{% endif %}{% if request.GET.course_id %}&course_id={{ request.GET.course_id }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                <p class="lead">Không tìm thấy đăng ký nào.</p>
                {% if request.GET.student_id or request.GET.course_id %}
                <a href="{% url 'enrollment-list' %}" class="btn btn-outline-primary mt-2">Xem tất cả đăng ký</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
