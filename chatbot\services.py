import re
import time
import uuid
import logging
from typing import Dict, List, Tuple, Optional
from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import Q, Avg
from students.models import Student
from courses.models import Course, Enrollment
from .models import FAQ, ChatSession, ChatHistory

# Cấu hình logging
logger = logging.getLogger(__name__)

class ChatbotService:
    """Service xử lý chatbot với PhoBERT và logic trả lời"""
    
    def __init__(self):
        self.intents = {
            'greeting': ['xin chào', 'chào', 'hello', 'hi', 'hey'],
            'student_info': ['thông tin sinh viên', 'sinh viên', 'học sinh', 'profile', 'hồ sơ'],
            'course_info': ['môn học', 'khóa học', 'course', 'subject', 'lớp học'],
            'enrollment': ['đăng ký', 'đăng ký học', 'enrollment', 'register'],
            'grade': ['điểm', 'điểm số', 'grade', 'mark', 'kết quả'],
            'schedule': ['lịch học', 'thời khóa biểu', 'schedule', 'timetable'],
            'help': ['giúp đỡ', 'help', 'hướng dẫn', 'guide'],
            'goodbye': ['tạm biệt', 'bye', 'goodbye', 'see you']
        }
        
        # Khởi tạo PhoBERT (sẽ được implement sau khi cài đặt dependencies)
        self.phobert_model = None
        self.tokenizer = None
        
    def get_or_create_session(self, user: User) -> ChatSession:
        """Lấy hoặc tạo session chat cho user"""
        # Tìm session đang hoạt động
        active_session = ChatSession.objects.filter(
            user=user, 
            is_active=True
        ).first()
        
        if active_session:
            return active_session
        
        # Tạo session mới
        session_id = str(uuid.uuid4())
        return ChatSession.objects.create(
            user=user,
            session_id=session_id
        )
    
    def detect_intent(self, message: str) -> Tuple[str, float]:
        """Phát hiện ý định từ tin nhắn"""
        message_lower = message.lower()
        
        # Simple keyword matching (sẽ được thay thế bằng PhoBERT)
        best_intent = 'general'
        best_score = 0.0
        
        for intent, keywords in self.intents.items():
            score = 0
            for keyword in keywords:
                if keyword in message_lower:
                    score += 1
            
            if score > best_score:
                best_score = score
                best_intent = intent
        
        # Normalize score
        intent_keywords = self.intents.get(best_intent, [])
        if intent_keywords:
            confidence = min(best_score / len(intent_keywords), 1.0)
        else:
            confidence = 0.0
        
        return best_intent, confidence
    
    def get_student_info(self, user: User, query: str) -> str:
        """Lấy thông tin sinh viên"""
        try:
            if user.is_staff:
                # Admin có thể tìm kiếm sinh viên khác
                if any(keyword in query.lower() for keyword in ['mã', 'id', 'student_id']):
                    # Trích xuất mã sinh viên từ query
                    student_id_match = re.search(r'[A-Za-z0-9]{5,}', query)
                    if student_id_match:
                        student_id = student_id_match.group()
                        try:
                            student = Student.objects.get(student_id=student_id)
                            return f"Thông tin sinh viên {student.full_name()}:\n" \
                                   f"- Mã SV: {student.student_id}\n" \
                                   f"- Email: {student.email}\n" \
                                   f"- Điện thoại: {student.phone_number}\n" \
                                   f"- Trạng thái: {'Đang học' if student.is_active else 'Đã nghỉ'}"
                        except Student.DoesNotExist:
                            return f"Không tìm thấy sinh viên có mã {student_id}"
                
                return "Vui lòng cung cấp mã sinh viên để tìm kiếm thông tin."
            else:
                # Sinh viên chỉ xem được thông tin của mình
                try:
                    student = Student.objects.get(email=user.email)
                    enrollments = Enrollment.objects.filter(student=student)
                    total_courses = enrollments.count()
                    avg_grade = enrollments.filter(numeric_grade__isnull=False).aggregate(
                        avg=Avg('numeric_grade')
                    )['avg']
                    
                    avg_grade_str = f"{avg_grade:.2f}" if avg_grade else "Chưa có"
                    return f"Thông tin của bạn:\n" \
                           f"- Họ tên: {student.full_name()}\n" \
                           f"- Mã SV: {student.student_id}\n" \
                           f"- Email: {student.email}\n" \
                           f"- Số môn đã đăng ký: {total_courses}\n" \
                           f"- Điểm trung bình: {avg_grade_str}"
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn trong hệ thống."
        except Exception as e:
            logger.error(f"Error getting student info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin sinh viên."
    
    def get_course_info(self, user: User, query: str) -> str:
        """Lấy thông tin môn học"""
        try:
            # Tìm kiếm môn học theo mã hoặc tên
            course_code_match = re.search(r'[A-Za-z0-9]{3,}', query)
            if course_code_match:
                course_code = course_code_match.group()
                courses = Course.objects.filter(
                    Q(course_code__icontains=course_code) | 
                    Q(name__icontains=course_code)
                )[:5]
            else:
                courses = Course.objects.filter(is_active=True)[:5]
            
            if courses:
                result = "Thông tin môn học:\n"
                for course in courses:
                    result += f"- {course.name} ({course.course_code})\n"
                    result += f"  Tín chỉ: {course.credits}, Học kỳ: {course.get_semester_display()}\n"
                    result += f"  Thời gian: {course.start_date} - {course.end_date}\n\n"
                return result
            else:
                return "Không tìm thấy môn học nào."
        except Exception as e:
            logger.error(f"Error getting course info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin môn học."
    
    def get_enrollment_info(self, user: User, query: str) -> str:
        """Lấy thông tin đăng ký học"""
        try:
            if not user.is_staff:
                try:
                    student = Student.objects.get(email=user.email)
                    enrollments = Enrollment.objects.filter(student=student).order_by('-enrollment_date')
                    
                    if enrollments:
                        result = "Danh sách môn học đã đăng ký:\n"
                        for enrollment in enrollments[:10]:
                            result += f"- {enrollment.course.name} ({enrollment.course.course_code})\n"
                            result += f"  Ngày đăng ký: {enrollment.enrollment_date}\n"
                            if enrollment.grade:
                                result += f"  Điểm: {enrollment.grade} ({enrollment.numeric_grade})\n"
                            result += "\n"
                        return result
                    else:
                        return "Bạn chưa đăng ký môn học nào."
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn."
            else:
                return "Vui lòng cung cấp mã sinh viên để xem thông tin đăng ký."
        except Exception as e:
            logger.error(f"Error getting enrollment info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin đăng ký."
    
    def search_faq(self, query: str) -> Optional[str]:
        """Tìm kiếm trong FAQ"""
        try:
            # Tìm kiếm trong câu hỏi và từ khóa
            faqs = FAQ.objects.filter(
                Q(question__icontains=query) |
                Q(keywords__icontains=query) |
                Q(answer__icontains=query),
                is_active=True
            )[:3]
            
            if faqs:
                result = "Tôi tìm thấy những thông tin sau:\n\n"
                for faq in faqs:
                    result += f"❓ {faq.question}\n"
                    result += f"💡 {faq.answer}\n\n"
                return result
            return None
        except Exception as e:
            logger.error(f"Error searching FAQ: {e}")
            return None
    
    def generate_response(self, user: User, message: str, intent: str, confidence: float) -> str:
        """Tạo phản hồi dựa trên intent"""

        # Kiểm tra quyền truy cập
        if not user.is_authenticated:
            return "Bạn cần đăng nhập để sử dụng chatbot."

        # Tìm trong FAQ trước
        faq_response = self.search_faq(message)
        if faq_response:
            return faq_response
        
        # Xử lý theo intent
        if intent == 'greeting':
            return f"Xin chào {'quản trị viên' if user.is_staff else 'bạn'}! Tôi là chatbot hỗ trợ hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?"
        
        elif intent == 'student_info':
            return self.get_student_info(user, message)
        
        elif intent == 'course_info':
            return self.get_course_info(user, message)
        
        elif intent == 'enrollment':
            return self.get_enrollment_info(user, message)
        
        elif intent == 'grade':
            return self.get_enrollment_info(user, message)  # Cùng logic với enrollment
        
        elif intent == 'help':
            help_text = """🤖 Tôi có thể giúp bạn với:

📚 **Thông tin môn học**: Hỏi về môn học, mã môn, tín chỉ
👨‍🎓 **Thông tin sinh viên**: Xem thông tin cá nhân, hồ sơ
📝 **Đăng ký học**: Kiểm tra môn đã đăng ký
📊 **Điểm số**: Xem điểm và kết quả học tập
❓ **Câu hỏi thường gặp**: Các thông tin hữu ích khác

Ví dụ câu hỏi:
- "Thông tin sinh viên của tôi"
- "Môn học IT001"
- "Danh sách môn đã đăng ký"
- "Điểm số của tôi" """
            return help_text
        
        elif intent == 'goodbye':
            return "Cảm ơn bạn đã sử dụng hệ thống! Chúc bạn học tập tốt! 👋"
        
        else:
            return "Xin lỗi, tôi chưa hiểu câu hỏi của bạn. Bạn có thể hỏi về thông tin sinh viên, môn học, đăng ký học, hoặc gõ 'help' để xem hướng dẫn."
    
    def process_message(self, user: User, message: str) -> Dict:
        """Xử lý tin nhắn từ user"""
        start_time = time.time()
        
        try:
            # Lấy hoặc tạo session
            session = self.get_or_create_session(user)
            
            # Lưu tin nhắn của user
            ChatHistory.objects.create(
                session=session,
                message_type='user',
                message=message
            )
            
            # Phát hiện intent
            intent, confidence = self.detect_intent(message)
            
            # Tạo phản hồi
            response = self.generate_response(user, message, intent, confidence)
            
            # Tính thời gian phản hồi
            response_time = time.time() - start_time
            
            # Lưu phản hồi của bot
            ChatHistory.objects.create(
                session=session,
                message_type='bot',
                message=response,
                intent=intent,
                confidence=confidence,
                response_time=response_time
            )
            
            return {
                'success': True,
                'response': response,
                'intent': intent,
                'confidence': confidence,
                'response_time': response_time
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                'success': False,
                'response': 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.',
                'error': str(e)
            }
