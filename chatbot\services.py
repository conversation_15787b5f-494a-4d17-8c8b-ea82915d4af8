import re
import time
import uuid
import logging
import unicodedata
from difflib import SequenceMatcher
from typing import Dict, List, Tuple, Optional
from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import Q, Avg
from students.models import Student
from courses.models import Course, Enrollment
from .models import FAQ, ChatSession, ChatHistory

# Cấu hình logging
logger = logging.getLogger(__name__)

class ChatbotService:
    """Service xử lý chatbot với PhoBERT và logic trả lời"""
    
    def __init__(self):
        self.intents = {
            'greeting': {
                'keywords': ['xin chào', 'chào', 'hello', 'hi', 'hey', 'chào bạn', 'xin chào bạn', 'chào bot', 'chào chatbot',
                           'chào em', 'chào anh', 'chào chị', 'good morning', 'good afternoon', 'good evening',
                           'chào buổi sáng', 'chào buổi chiều', 'chào buổi tối', 'hế lô', 'alo'],
                'patterns': ['chào', 'hello', 'hi', 'xin chào', 'chào bạn', 'hey', 'hế lô'],
                'synonyms': ['chào hỏi', 'lời chào', 'greeting', 'salutation']
            },
            'student_info': {
                'keywords': ['thông tin sinh viên', 'sinh viên', 'học sinh', 'profile', 'hồ sơ', 'thông tin cá nhân',
                           'thông tin của tôi', 'hồ sơ cá nhân', 'thông tin học sinh', 'profile sinh viên',
                           'xem thông tin', 'thông tin bản thân', 'hồ sơ học tập', 'info sinh viên',
                           'tôi là ai', 'tôi là sinh viên nào', 'mã sinh viên của tôi', 'tên tôi là gì',
                           'thông tin về tôi', 'profile của tôi', 'hồ sơ của tôi', 'chi tiết sinh viên',
                           'thông tin học tập', 'dữ liệu cá nhân', 'thông tin cơ bản'],
                'patterns': ['thông tin', 'sinh viên', 'hồ sơ', 'profile', 'cá nhân', 'của tôi', 'tôi'],
                'synonyms': ['student info', 'student profile', 'personal info', 'my info', 'about me']
            },
            'course_info': {
                'keywords': ['môn học', 'khóa học', 'course', 'subject', 'lớp học', 'thông tin môn học',
                           'danh sách môn học', 'môn', 'học phần', 'subject info', 'course info',
                           'tìm môn học', 'tra cứu môn học', 'xem môn học'],
                'patterns': ['môn học', 'course', 'subject', 'môn', 'học phần'],
                'synonyms': ['course information', 'subject info', 'class info']
            },
            'enrollment': {
                'keywords': ['đăng ký', 'đăng ký học', 'enrollment', 'register', 'đăng ký môn học',
                           'danh sách đăng ký', 'môn đã đăng ký', 'đã đăng ký', 'enroll',
                           'registration', 'đăng ký học phần', 'môn học đã đăng ký',
                           'xem đăng ký', 'lịch sử đăng ký', 'môn của tôi', 'my courses',
                           'các môn tôi đăng ký', 'môn học của tôi', 'tôi đăng ký môn gì',
                           'danh sách môn', 'môn tôi học', 'học phần đã đăng ký',
                           'tôi có môn nào', 'môn nào tôi đăng ký', 'course list',
                           'danh sách môn đã đăng ký', 'các môn tôi học', 'course list của tôi',
                           'enroll courses', 'subjects của tôi', 'môn đã enroll'],
                'patterns': ['đăng ký', 'enrollment', 'register', 'đã đăng ký', 'môn của tôi', 'my courses', 'tôi học', 'tôi đăng ký'],
                'synonyms': ['registration', 'course registration', 'enroll', 'my subjects', 'enrolled courses']
            },
            'grade': {
                'keywords': ['điểm', 'điểm số', 'grade', 'mark', 'kết quả', 'điểm thi', 'điểm học tập',
                           'xem điểm', 'tra cứu điểm', 'điểm của tôi', 'kết quả học tập',
                           'điểm trung bình', 'GPA', 'transcript', 'bảng điểm', 'điểm số của tôi',
                           'tôi được bao nhiêu điểm', 'điểm tôi', 'kết quả thi', 'điểm kiểm tra',
                           'điểm cuối kỳ', 'điểm giữa kỳ', 'điểm tổng kết', 'thành tích học tập',
                           'kết quả học tập của tôi', 'điểm các môn', 'điểm từng môn'],
                'patterns': ['điểm', 'grade', 'mark', 'kết quả', 'GPA', 'của tôi', 'tôi'],
                'synonyms': ['score', 'result', 'academic result', 'my grade', 'my score']
            },
            'schedule': {
                'keywords': ['lịch học', 'thời khóa biểu', 'schedule', 'timetable', 'lịch', 'tkb',
                           'lịch học tập', 'thời gian học', 'giờ học', 'lịch trình học tập'],
                'patterns': ['lịch', 'schedule', 'timetable', 'thời khóa biểu'],
                'synonyms': ['class schedule', 'study schedule']
            },
            'help': {
                'keywords': ['giúp đỡ', 'help', 'hướng dẫn', 'guide', 'trợ giúp', 'hỗ trợ',
                           'làm thế nào', 'how to', 'cách', 'hướng dẫn sử dụng',
                           'giúp tôi', 'support', 'assistance'],
                'patterns': ['help', 'giúp', 'hướng dẫn', 'hỗ trợ', 'trợ giúp'],
                'synonyms': ['support', 'assistance', 'guidance']
            },
            'goodbye': {
                'keywords': ['tạm biệt', 'bye', 'goodbye', 'see you', 'chào tạm biệt',
                           'hẹn gặp lại', 'kết thúc', 'exit', 'quit'],
                'patterns': ['bye', 'goodbye', 'tạm biệt', 'chào'],
                'synonyms': ['farewell', 'see you later']
            }
        }
        
        # Khởi tạo PhoBERT (sẽ được implement sau khi cài đặt dependencies)
        self.phobert_model = None
        self.tokenizer = None

        # Threshold cho similarity matching
        self.similarity_threshold = 0.5  # Giảm để tìm được nhiều FAQ hơn
        self.intent_threshold = 0.25      # Giảm để nhận diện intent tốt hơn

        # Context tracking
        self.context_keywords = {
            'student_info': ['sinh viên', 'thông tin', 'hồ sơ'],
            'course_info': ['môn học', 'course', 'subject'],
            'enrollment': ['đăng ký', 'môn đã đăng ký'],
            'grade': ['điểm', 'kết quả', 'GPA']
        }

    def normalize_text(self, text: str) -> str:
        """Chuẩn hóa text: loại bỏ dấu, chuyển thường, loại bỏ ký tự đặc biệt"""
        if not text:
            return ""

        # Chuyển thường
        text = text.lower().strip()

        # Loại bỏ dấu tiếng Việt
        text = unicodedata.normalize('NFD', text)
        text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')

        # Loại bỏ ký tự đặc biệt, chỉ giữ chữ cái, số và khoảng trắng
        text = re.sub(r'[^\w\s]', ' ', text)

        # Loại bỏ khoảng trắng thừa
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Tính độ tương đồng giữa 2 chuỗi text"""
        if not text1 or not text2:
            return 0.0

        # Chuẩn hóa text
        norm_text1 = self.normalize_text(text1)
        norm_text2 = self.normalize_text(text2)

        # Sử dụng SequenceMatcher để tính similarity
        similarity = SequenceMatcher(None, norm_text1, norm_text2).ratio()

        # Kiểm tra substring matching
        if norm_text1 in norm_text2 or norm_text2 in norm_text1:
            similarity = max(similarity, 0.8)

        return similarity

    def fuzzy_match_keywords(self, message: str, keywords: List[str]) -> float:
        """Tìm kiếm fuzzy trong danh sách keywords"""
        if not keywords:
            return 0.0

        normalized_message = self.normalize_text(message)
        max_similarity = 0.0

        for keyword in keywords:
            similarity = self.calculate_similarity(normalized_message, keyword)
            max_similarity = max(max_similarity, similarity)

            # Kiểm tra partial match
            normalized_keyword = self.normalize_text(keyword)
            if normalized_keyword in normalized_message:
                max_similarity = max(max_similarity, 0.9)

        return max_similarity

    def get_conversation_context(self, session: ChatSession, limit: int = 5) -> List[str]:
        """Lấy ngữ cảnh từ lịch sử cuộc trò chuyện"""
        try:
            recent_messages = ChatHistory.objects.filter(
                session=session,
                message_type='user'
            ).order_by('-timestamp')[:limit]

            return [msg.message for msg in reversed(recent_messages)]
        except Exception:
            return []

    def enhance_intent_with_context(self, message: str, session: ChatSession, intent: str, confidence: float) -> Tuple[str, float]:
        """Cải thiện intent detection bằng context"""
        try:
            # Nếu confidence đã cao, không cần context
            if confidence > 0.7:
                return intent, confidence

            # Lấy context từ lịch sử
            context_messages = self.get_conversation_context(session)

            if not context_messages:
                return intent, confidence

            # Tìm intent phổ biến trong context
            context_intents = {}
            for ctx_msg in context_messages:
                ctx_intent, _ = self.detect_intent(ctx_msg)
                if ctx_intent != 'general':
                    context_intents[ctx_intent] = context_intents.get(ctx_intent, 0) + 1

            # Nếu có intent phổ biến trong context và tin nhắn hiện tại mơ hồ
            if context_intents and confidence < 0.5:
                most_common_intent = max(context_intents.items(), key=lambda x: x[1])[0]

                # Kiểm tra xem tin nhắn hiện tại có liên quan đến context intent không
                context_keywords = self.context_keywords.get(most_common_intent, [])
                if any(self.calculate_similarity(message, keyword) > 0.3 for keyword in context_keywords):
                    return most_common_intent, min(confidence + 0.3, 1.0)

            return intent, confidence
        except Exception as e:
            logger.error(f"Error enhancing intent with context: {e}")
            return intent, confidence

    def get_suggested_questions(self, message: str) -> List[str]:
        """Gợi ý câu hỏi tương tự khi không hiểu"""
        suggestions = [
            "Thông tin sinh viên của tôi",
            "Danh sách môn đã đăng ký",
            "Điểm số của tôi",
            "Môn học IT001",
            "Hướng dẫn sử dụng hệ thống",
            "Làm thế nào để đăng ký môn học?",
            "Cách tính điểm trung bình",
            "Thông tin liên hệ phòng đào tạo"
        ]

        # Tìm gợi ý phù hợp nhất dựa trên similarity
        scored_suggestions = []
        for suggestion in suggestions:
            score = self.calculate_similarity(message, suggestion)
            if score > 0.2:  # Threshold thấp để có nhiều gợi ý
                scored_suggestions.append((suggestion, score))

        # Sắp xếp và lấy top 3
        scored_suggestions.sort(key=lambda x: x[1], reverse=True)
        return [s[0] for s in scored_suggestions[:3]]

    def generate_fallback_response(self, message: str) -> str:
        """Tạo phản hồi khi không hiểu câu hỏi"""
        suggestions = self.get_suggested_questions(message)

        response = "Xin lỗi, tôi chưa hiểu rõ câu hỏi của bạn. "

        if suggestions:
            response += "Có phải bạn muốn hỏi về:\n\n"
            for i, suggestion in enumerate(suggestions, 1):
                response += f"{i}. {suggestion}\n"
            response += "\nBạn có thể thử hỏi lại bằng cách khác hoặc chọn một trong các gợi ý trên."
        else:
            response += "Bạn có thể thử:\n\n"
            response += "• Hỏi về thông tin sinh viên: 'thông tin của tôi'\n"
            response += "• Xem môn đã đăng ký: 'môn học của tôi'\n"
            response += "• Kiểm tra điểm số: 'điểm của tôi'\n"
            response += "• Tìm hiểu môn học: 'môn học [tên môn]'\n"
            response += "• Xem hướng dẫn: 'help'\n\n"
            response += "Hoặc gõ 'help' để xem danh sách đầy đủ các câu hỏi tôi có thể trả lời."

        return response
        
    def get_or_create_session(self, user: User) -> ChatSession:
        """Lấy hoặc tạo session chat cho user"""
        # Tìm session đang hoạt động
        active_session = ChatSession.objects.filter(
            user=user, 
            is_active=True
        ).first()
        
        if active_session:
            return active_session
        
        # Tạo session mới
        session_id = str(uuid.uuid4())
        return ChatSession.objects.create(
            user=user,
            session_id=session_id
        )
    
    def detect_intent(self, message: str) -> Tuple[str, float]:
        """Phát hiện ý định từ tin nhắn sử dụng fuzzy matching"""
        if not message.strip():
            return 'general', 0.0

        best_intent = 'general'
        best_confidence = 0.0

        for intent_name, intent_data in self.intents.items():
            # Tính điểm cho keywords
            keyword_score = self.fuzzy_match_keywords(message, intent_data['keywords'])

            # Tính điểm cho patterns
            pattern_score = self.fuzzy_match_keywords(message, intent_data['patterns'])

            # Tính điểm cho synonyms
            synonym_score = self.fuzzy_match_keywords(message, intent_data['synonyms'])

            # Tổng hợp điểm với trọng số (tăng trọng số cho keyword)
            total_score = (keyword_score * 0.7 + pattern_score * 0.2 + synonym_score * 0.1)

            # Bonus cho exact match
            normalized_message = self.normalize_text(message)
            for keyword in intent_data['keywords']:
                if self.normalize_text(keyword) in normalized_message:
                    total_score += 0.2
                    break

            # Cập nhật best intent nếu điểm cao hơn
            if total_score > best_confidence:
                best_confidence = total_score
                best_intent = intent_name

        # Nếu confidence quá thấp, trả về general
        if best_confidence < self.intent_threshold:
            best_intent = 'general'
            best_confidence = 0.0

        return best_intent, min(best_confidence, 1.0)
    
    def get_student_info(self, user: User, query: str) -> str:
        """Lấy thông tin sinh viên"""
        try:
            if user.is_staff:
                # Admin có thể tìm kiếm sinh viên khác
                if any(keyword in query.lower() for keyword in ['mã', 'id', 'student_id']):
                    # Trích xuất mã sinh viên từ query
                    student_id_match = re.search(r'[A-Za-z0-9]{5,}', query)
                    if student_id_match:
                        student_id = student_id_match.group()
                        try:
                            student = Student.objects.get(student_id=student_id)
                            return f"Thông tin sinh viên {student.full_name()}:\n" \
                                   f"- Mã SV: {student.student_id}\n" \
                                   f"- Email: {student.email}\n" \
                                   f"- Điện thoại: {student.phone_number}\n" \
                                   f"- Trạng thái: {'Đang học' if student.is_active else 'Đã nghỉ'}"
                        except Student.DoesNotExist:
                            return f"Không tìm thấy sinh viên có mã {student_id}"
                
                return "Vui lòng cung cấp mã sinh viên để tìm kiếm thông tin."
            else:
                # Sinh viên chỉ xem được thông tin của mình
                try:
                    student = Student.objects.get(email=user.email)
                    enrollments = Enrollment.objects.filter(student=student)
                    total_courses = enrollments.count()
                    avg_grade = enrollments.filter(numeric_grade__isnull=False).aggregate(
                        avg=Avg('numeric_grade')
                    )['avg']
                    
                    avg_grade_str = f"{avg_grade:.2f}" if avg_grade else "Chưa có"
                    return f"Thông tin của bạn:\n" \
                           f"- Họ tên: {student.full_name()}\n" \
                           f"- Mã SV: {student.student_id}\n" \
                           f"- Email: {student.email}\n" \
                           f"- Số môn đã đăng ký: {total_courses}\n" \
                           f"- Điểm trung bình: {avg_grade_str}"
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn trong hệ thống."
        except Exception as e:
            logger.error(f"Error getting student info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin sinh viên."
    
    def get_course_info(self, user: User, query: str) -> str:
        """Lấy thông tin môn học"""
        try:
            # Tìm kiếm môn học theo mã hoặc tên
            course_code_match = re.search(r'[A-Za-z0-9]{3,}', query)
            if course_code_match:
                course_code = course_code_match.group()
                courses = Course.objects.filter(
                    Q(course_code__icontains=course_code) | 
                    Q(name__icontains=course_code)
                )[:5]
            else:
                courses = Course.objects.filter(is_active=True)[:5]
            
            if courses:
                result = "Thông tin môn học:\n"
                for course in courses:
                    result += f"- {course.name} ({course.course_code})\n"
                    result += f"  Tín chỉ: {course.credits}, Học kỳ: {course.get_semester_display()}\n"
                    result += f"  Thời gian: {course.start_date} - {course.end_date}\n\n"
                return result
            else:
                return "Không tìm thấy môn học nào."
        except Exception as e:
            logger.error(f"Error getting course info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin môn học."
    
    def get_enrollment_info(self, user: User, query: str) -> str:
        """Lấy thông tin đăng ký học"""
        try:
            if not user.is_staff:
                try:
                    student = Student.objects.get(email=user.email)
                    enrollments = Enrollment.objects.filter(student=student).order_by('-enrollment_date')
                    
                    if enrollments:
                        result = "Danh sách môn học đã đăng ký:\n"
                        for enrollment in enrollments[:10]:
                            result += f"- {enrollment.course.name} ({enrollment.course.course_code})\n"
                            result += f"  Ngày đăng ký: {enrollment.enrollment_date}\n"
                            if enrollment.grade:
                                result += f"  Điểm: {enrollment.grade} ({enrollment.numeric_grade})\n"
                            result += "\n"
                        return result
                    else:
                        return "Bạn chưa đăng ký môn học nào."
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn."
            else:
                return "Vui lòng cung cấp mã sinh viên để xem thông tin đăng ký."
        except Exception as e:
            logger.error(f"Error getting enrollment info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin đăng ký."
    
    def search_faq(self, query: str) -> Optional[str]:
        """Tìm kiếm trong FAQ với fuzzy matching"""
        try:
            if not query.strip():
                return None

            # Lấy tất cả FAQ đang hoạt động
            all_faqs = FAQ.objects.filter(is_active=True)

            # Tính điểm similarity cho mỗi FAQ
            faq_scores = []

            for faq in all_faqs:
                # Tính điểm cho question
                question_score = self.calculate_similarity(query, faq.question)

                # Tính điểm cho keywords
                keyword_score = 0.0
                if faq.keywords:
                    keywords = [kw.strip() for kw in faq.keywords.split(',')]
                    keyword_score = self.fuzzy_match_keywords(query, keywords)

                # Tính điểm cho answer (trọng số thấp hơn)
                answer_score = self.calculate_similarity(query, faq.answer) * 0.3

                # Tổng điểm
                total_score = max(question_score, keyword_score) + answer_score

                if total_score > self.similarity_threshold:
                    faq_scores.append((faq, total_score))

            # Sắp xếp theo điểm và lấy top 3
            faq_scores.sort(key=lambda x: x[1], reverse=True)
            top_faqs = faq_scores[:3]

            if top_faqs:
                result = "Tôi tìm thấy những thông tin sau:\n\n"
                for faq, score in top_faqs:
                    result += f"❓ {faq.question}\n"
                    result += f"💡 {faq.answer}\n"
                    result += f"📊 Độ chính xác: {score:.0%}\n\n"
                return result

            return None
        except Exception as e:
            logger.error(f"Error searching FAQ: {e}")
            return None
    
    def generate_response(self, user: User, message: str, intent: str, confidence: float) -> str:
        """Tạo phản hồi dựa trên intent"""

        # Kiểm tra quyền truy cập
        if not user.is_authenticated:
            return "Bạn cần đăng nhập để sử dụng chatbot."

        # Tìm trong FAQ trước
        faq_response = self.search_faq(message)
        if faq_response:
            return faq_response
        
        # Xử lý theo intent
        if intent == 'greeting':
            return f"Xin chào {'quản trị viên' if user.is_staff else 'bạn'}! Tôi là chatbot hỗ trợ hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?"
        
        elif intent == 'student_info':
            return self.get_student_info(user, message)
        
        elif intent == 'course_info':
            return self.get_course_info(user, message)
        
        elif intent == 'enrollment':
            return self.get_enrollment_info(user, message)
        
        elif intent == 'grade':
            return self.get_enrollment_info(user, message)  # Cùng logic với enrollment
        
        elif intent == 'help':
            help_text = """🤖 Tôi có thể giúp bạn với:

📚 **Thông tin môn học**: Hỏi về môn học, mã môn, tín chỉ
👨‍🎓 **Thông tin sinh viên**: Xem thông tin cá nhân, hồ sơ
📝 **Đăng ký học**: Kiểm tra môn đã đăng ký
📊 **Điểm số**: Xem điểm và kết quả học tập
❓ **Câu hỏi thường gặp**: Các thông tin hữu ích khác

Ví dụ câu hỏi:
- "Thông tin sinh viên của tôi"
- "Môn học IT001"
- "Danh sách môn đã đăng ký"
- "Điểm số của tôi" """
            return help_text
        
        elif intent == 'goodbye':
            return "Cảm ơn bạn đã sử dụng hệ thống! Chúc bạn học tập tốt! 👋"
        
        else:
            return self.generate_fallback_response(message)
    
    def process_message(self, user: User, message: str) -> Dict:
        """Xử lý tin nhắn từ user"""
        start_time = time.time()
        
        try:
            # Lấy hoặc tạo session
            session = self.get_or_create_session(user)
            
            # Lưu tin nhắn của user
            ChatHistory.objects.create(
                session=session,
                message_type='user',
                message=message
            )
            
            # Phát hiện intent
            intent, confidence = self.detect_intent(message)

            # Cải thiện intent với context
            intent, confidence = self.enhance_intent_with_context(message, session, intent, confidence)

            # Tạo phản hồi
            response = self.generate_response(user, message, intent, confidence)
            
            # Tính thời gian phản hồi
            response_time = time.time() - start_time
            
            # Lưu phản hồi của bot
            ChatHistory.objects.create(
                session=session,
                message_type='bot',
                message=response,
                intent=intent,
                confidence=confidence,
                response_time=response_time
            )
            
            return {
                'success': True,
                'response': response,
                'intent': intent,
                'confidence': confidence,
                'response_time': response_time
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                'success': False,
                'response': 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.',
                'error': str(e)
            }
