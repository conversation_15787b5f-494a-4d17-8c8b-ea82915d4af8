import re
import time
import uuid
import logging
import unicodedata
from difflib import SequenceMatcher
from typing import Dict, List, Tuple, Optional
from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import Q, Avg
from students.models import Student
from courses.models import Course, Enrollment
from .models import FAQ, ChatSession, ChatHistory

# Cấu hình logging
logger = logging.getLogger(__name__)

class ChatbotService:
    """Service xử lý chatbot với PhoBERT và logic trả lời"""
    
    def __init__(self):
        self.intents = {
            'greeting': {
                'keywords': ['xin chào', 'hello', 'hi', 'hey', 'chào bạn', 'xin chào bạn', 'chào bot', 'chào chatbot',
                           'chào em', 'chào anh', 'chào chị', 'good morning', 'good afternoon', 'good evening',
                           'chào buổi sáng', 'chào buổi chiều', 'chào buổi tối', 'hế lô', 'alo'],
                'patterns': ['hello', 'hi', 'xin chào', 'chào bạn', 'hey', 'hế lô'],  # Loại bỏ 'chào' đơn lẻ
                'synonyms': ['chào hỏi', 'lời chào', 'greeting', 'salutation']
            },
            'student_info': {
                'keywords': ['thông tin sinh viên', 'sinh viên', 'học sinh', 'profile', 'hồ sơ', 'thông tin cá nhân',
                           'thông tin của tôi', 'hồ sơ cá nhân', 'thông tin học sinh', 'profile sinh viên',
                           'xem thông tin', 'thông tin bản thân', 'hồ sơ học tập', 'info sinh viên',
                           'tôi là ai', 'tôi là sinh viên nào', 'mã sinh viên của tôi', 'tên tôi là gì',
                           'thông tin về tôi', 'profile của tôi', 'hồ sơ của tôi', 'chi tiết sinh viên',
                           'thông tin học tập', 'dữ liệu cá nhân', 'thông tin cơ bản'],
                'patterns': ['thông tin', 'sinh viên', 'hồ sơ', 'profile', 'cá nhân', 'của tôi', 'tôi'],
                'synonyms': ['student info', 'student profile', 'personal info', 'my info', 'about me']
            },
            'course_info': {
                'keywords': ['môn học', 'khóa học', 'course', 'subject', 'lớp học', 'thông tin môn học',
                           'danh sách môn học', 'môn', 'học phần', 'subject info', 'course info',
                           'tìm môn học', 'tra cứu môn học', 'xem môn học'],
                'patterns': ['môn học', 'course', 'subject', 'môn', 'học phần'],
                'synonyms': ['course information', 'subject info', 'class info']
            },
            'enrollment': {
                'keywords': ['đăng ký', 'đăng ký học', 'enrollment', 'register', 'đăng ký môn học',
                           'danh sách đăng ký', 'môn đã đăng ký', 'đã đăng ký', 'enroll',
                           'registration', 'đăng ký học phần', 'môn học đã đăng ký',
                           'xem đăng ký', 'lịch sử đăng ký', 'môn của tôi', 'my courses',
                           'các môn tôi đăng ký', 'môn học của tôi', 'tôi đăng ký môn gì',
                           'danh sách môn', 'môn tôi học', 'học phần đã đăng ký',
                           'tôi có môn nào', 'môn nào tôi đăng ký', 'course list',
                           'danh sách môn đã đăng ký', 'các môn tôi học', 'course list của tôi',
                           'enroll courses', 'subjects của tôi', 'môn đã enroll'],
                'patterns': ['đăng ký', 'enrollment', 'register', 'đã đăng ký', 'môn của tôi', 'my courses', 'tôi học', 'tôi đăng ký'],
                'synonyms': ['registration', 'course registration', 'enroll', 'my subjects', 'enrolled courses']
            },
            'grade': {
                'keywords': ['điểm', 'điểm số', 'grade', 'mark', 'kết quả', 'điểm thi', 'điểm học tập',
                           'xem điểm', 'tra cứu điểm', 'điểm của tôi', 'kết quả học tập',
                           'điểm trung bình', 'GPA', 'transcript', 'bảng điểm', 'điểm số của tôi',
                           'tôi được bao nhiêu điểm', 'điểm tôi', 'kết quả thi', 'điểm kiểm tra',
                           'điểm cuối kỳ', 'điểm giữa kỳ', 'điểm tổng kết', 'thành tích học tập',
                           'kết quả học tập của tôi', 'điểm các môn', 'điểm từng môn'],
                'patterns': ['điểm', 'grade', 'mark', 'kết quả', 'GPA', 'của tôi', 'tôi'],
                'synonyms': ['score', 'result', 'academic result', 'my grade', 'my score']
            },
            'schedule': {
                'keywords': ['lịch học', 'thời khóa biểu', 'schedule', 'timetable', 'lịch', 'tkb',
                           'lịch học tập', 'thời gian học', 'giờ học', 'lịch trình học tập'],
                'patterns': ['lịch', 'schedule', 'timetable', 'thời khóa biểu'],
                'synonyms': ['class schedule', 'study schedule']
            },
            'help': {
                'keywords': ['giúp đỡ', 'help', 'hướng dẫn', 'guide', 'trợ giúp', 'hỗ trợ',
                           'làm thế nào', 'how to', 'cách', 'hướng dẫn sử dụng',
                           'giúp tôi', 'support', 'assistance'],
                'patterns': ['help', 'giúp', 'hướng dẫn', 'hỗ trợ', 'trợ giúp'],
                'synonyms': ['support', 'assistance', 'guidance']
            },
            'goodbye': {
                'keywords': ['tạm biệt', 'bye', 'goodbye', 'see you', 'chào tạm biệt',
                           'hẹn gặp lại', 'kết thúc', 'exit', 'quit'],
                'patterns': ['bye', 'goodbye', 'tạm biệt', 'chào'],
                'synonyms': ['farewell', 'see you later']
            }
        }
        
        # Khởi tạo PhoBERT (sẽ được implement sau khi cài đặt dependencies)
        self.phobert_model = None
        self.tokenizer = None

        # Threshold cho similarity matching
        self.similarity_threshold = 0.5  # Giảm để tìm được nhiều FAQ hơn
        self.intent_threshold = 0.25      # Giảm để nhận diện intent tốt hơn

        # Context tracking
        self.context_keywords = {
            'student_info': ['sinh viên', 'thông tin', 'hồ sơ'],
            'course_info': ['môn học', 'course', 'subject'],
            'enrollment': ['đăng ký', 'môn đã đăng ký'],
            'grade': ['điểm', 'kết quả', 'GPA']
        }

    def normalize_text(self, text: str) -> str:
        """Chuẩn hóa text: loại bỏ dấu, chuyển thường, loại bỏ ký tự đặc biệt"""
        if not text:
            return ""

        # Chuyển thường
        text = text.lower().strip()

        # Loại bỏ dấu tiếng Việt
        text = unicodedata.normalize('NFD', text)
        text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')

        # Loại bỏ ký tự đặc biệt, chỉ giữ chữ cái, số và khoảng trắng
        text = re.sub(r'[^\w\s]', ' ', text)

        # Loại bỏ khoảng trắng thừa
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Tính độ tương đồng giữa 2 chuỗi text"""
        if not text1 or not text2:
            return 0.0

        # Chuẩn hóa text
        norm_text1 = self.normalize_text(text1)
        norm_text2 = self.normalize_text(text2)

        # Sử dụng SequenceMatcher để tính similarity
        similarity = SequenceMatcher(None, norm_text1, norm_text2).ratio()

        # Kiểm tra substring matching
        if norm_text1 in norm_text2 or norm_text2 in norm_text1:
            similarity = max(similarity, 0.8)

        return similarity

    def fuzzy_match_keywords(self, message: str, keywords: List[str]) -> float:
        """Tìm kiếm fuzzy trong danh sách keywords"""
        if not keywords:
            return 0.0

        normalized_message = self.normalize_text(message)
        max_similarity = 0.0

        for keyword in keywords:
            similarity = self.calculate_similarity(normalized_message, keyword)
            max_similarity = max(max_similarity, similarity)

            # Kiểm tra partial match
            normalized_keyword = self.normalize_text(keyword)
            if normalized_keyword in normalized_message:
                max_similarity = max(max_similarity, 0.9)

        return max_similarity

    def extract_entities(self, message: str) -> Dict[str, str]:
        """Trích xuất thông tin cụ thể từ câu hỏi"""
        entities = {}

        # Trích xuất mã môn học (pattern: 2-3 chữ cái + 3-4 số)
        course_code_pattern = r'\b[A-Za-z]{2,4}\d{3,4}\b'
        course_codes = re.findall(course_code_pattern, message.upper())
        if course_codes:
            entities['course_code'] = course_codes[0]

        # Trích xuất mã sinh viên (pattern: số hoặc chữ+số)
        student_id_pattern = r'\b(?:\d{8,10}|[A-Za-z]\d{7,9})\b'
        student_ids = re.findall(student_id_pattern, message)
        if student_ids:
            entities['student_id'] = student_ids[0]

        # Trích xuất năm học
        year_pattern = r'\b20\d{2}\b'
        years = re.findall(year_pattern, message)
        if years:
            entities['year'] = years[0]

        # Trích xuất học kỳ
        semester_keywords = {
            'học kỳ 1': '1', 'hk1': '1', 'semester 1': '1',
            'học kỳ 2': '2', 'hk2': '2', 'semester 2': '2',
            'học kỳ hè': '3', 'hè': '3', 'summer': '3'
        }

        normalized_msg = self.normalize_text(message)
        for keyword, value in semester_keywords.items():
            if self.normalize_text(keyword) in normalized_msg:
                entities['semester'] = value
                break

        return entities

    def analyze_query_intent(self, message: str, entities: Dict[str, str]) -> Dict[str, any]:
        """Phân tích chi tiết ý định của câu hỏi"""
        analysis = {
            'specific_request': None,
            'target_info': [],
            'scope': 'general',
            'entities': entities
        }

        normalized_msg = self.normalize_text(message)

        # Phân tích yêu cầu cụ thể
        if any(word in normalized_msg for word in ['tat ca', 'danh sach', 'list', 'all']):
            analysis['scope'] = 'all'
        elif any(word in normalized_msg for word in ['chi tiet', 'detail', 'thong tin day du']):
            analysis['scope'] = 'detailed'
        elif entities.get('course_code') or entities.get('student_id'):
            analysis['scope'] = 'specific'

        # Xác định thông tin cần trả về
        info_keywords = {
            'diem': ['grade', 'score', 'mark'],
            'ten': ['name', 'title'],
            'ma': ['code', 'id'],
            'tin chi': ['credit', 'credits'],
            'lich': ['schedule', 'time'],
            'giang vien': ['teacher', 'instructor'],
            'phong': ['room', 'classroom'],
            'ngay': ['date', 'time'],
            'trang thai': ['status', 'state']
        }

        for info_type, keywords in info_keywords.items():
            if any(self.calculate_similarity(normalized_msg, keyword) > 0.3 for keyword in [info_type] + keywords):
                analysis['target_info'].append(info_type)

        return analysis

    def get_conversation_context(self, session: ChatSession, limit: int = 5) -> List[str]:
        """Lấy ngữ cảnh từ lịch sử cuộc trò chuyện"""
        try:
            recent_messages = ChatHistory.objects.filter(
                session=session,
                message_type='user'
            ).order_by('-timestamp')[:limit]

            return [msg.message for msg in reversed(recent_messages)]
        except Exception:
            return []

    def enhance_intent_with_context(self, message: str, session: ChatSession, intent: str, confidence: float) -> Tuple[str, float]:
        """Cải thiện intent detection bằng context"""
        try:
            # Nếu confidence đã cao, không cần context
            if confidence > 0.7:
                return intent, confidence

            # Lấy context từ lịch sử
            context_messages = self.get_conversation_context(session)

            if not context_messages:
                return intent, confidence

            # Tìm intent phổ biến trong context
            context_intents = {}
            for ctx_msg in context_messages:
                ctx_intent, _ = self.detect_intent(ctx_msg)
                if ctx_intent != 'general':
                    context_intents[ctx_intent] = context_intents.get(ctx_intent, 0) + 1

            # Nếu có intent phổ biến trong context và tin nhắn hiện tại mơ hồ
            if context_intents and confidence < 0.5:
                most_common_intent = max(context_intents.items(), key=lambda x: x[1])[0]

                # Kiểm tra xem tin nhắn hiện tại có liên quan đến context intent không
                context_keywords = self.context_keywords.get(most_common_intent, [])
                if any(self.calculate_similarity(message, keyword) > 0.3 for keyword in context_keywords):
                    return most_common_intent, min(confidence + 0.3, 1.0)

            return intent, confidence
        except Exception as e:
            logger.error(f"Error enhancing intent with context: {e}")
            return intent, confidence

    def get_suggested_questions(self, message: str) -> List[str]:
        """Gợi ý câu hỏi tương tự khi không hiểu"""
        suggestions = [
            "Thông tin sinh viên của tôi",
            "Danh sách môn đã đăng ký",
            "Điểm số của tôi",
            "Môn học IT001",
            "Hướng dẫn sử dụng hệ thống",
            "Làm thế nào để đăng ký môn học?",
            "Cách tính điểm trung bình",
            "Thông tin liên hệ phòng đào tạo"
        ]

        # Tìm gợi ý phù hợp nhất dựa trên similarity
        scored_suggestions = []
        for suggestion in suggestions:
            score = self.calculate_similarity(message, suggestion)
            if score > 0.2:  # Threshold thấp để có nhiều gợi ý
                scored_suggestions.append((suggestion, score))

        # Sắp xếp và lấy top 3
        scored_suggestions.sort(key=lambda x: x[1], reverse=True)
        return [s[0] for s in scored_suggestions[:3]]

    def generate_fallback_response(self, message: str) -> str:
        """Tạo phản hồi khi không hiểu câu hỏi"""
        suggestions = self.get_suggested_questions(message)

        response = "Xin lỗi, tôi chưa hiểu rõ câu hỏi của bạn. "

        if suggestions:
            response += "Có phải bạn muốn hỏi về:\n\n"
            for i, suggestion in enumerate(suggestions, 1):
                response += f"{i}. {suggestion}\n"
            response += "\nBạn có thể thử hỏi lại bằng cách khác hoặc chọn một trong các gợi ý trên."
        else:
            response += "Bạn có thể thử:\n\n"
            response += "• Hỏi về thông tin sinh viên: 'thông tin của tôi'\n"
            response += "• Xem môn đã đăng ký: 'môn học của tôi'\n"
            response += "• Kiểm tra điểm số: 'điểm của tôi'\n"
            response += "• Tìm hiểu môn học: 'môn học [tên môn]'\n"
            response += "• Xem hướng dẫn: 'help'\n\n"
            response += "Hoặc gõ 'help' để xem danh sách đầy đủ các câu hỏi tôi có thể trả lời."

        return response
        
    def get_or_create_session(self, user: User) -> ChatSession:
        """Lấy hoặc tạo session chat cho user"""
        # Tìm session đang hoạt động
        active_session = ChatSession.objects.filter(
            user=user, 
            is_active=True
        ).first()
        
        if active_session:
            return active_session
        
        # Tạo session mới
        session_id = str(uuid.uuid4())
        return ChatSession.objects.create(
            user=user,
            session_id=session_id
        )
    
    def detect_intent(self, message: str) -> Tuple[str, float]:
        """Phát hiện ý định từ tin nhắn sử dụng fuzzy matching"""
        if not message.strip():
            return 'general', 0.0

        best_intent = 'general'
        best_confidence = 0.0

        # Negative keywords để tránh false positives
        negative_keywords = {
            'greeting': ['thông tin', 'điểm', 'môn', 'đăng ký', 'sinh viên', 'course', 'grade', 'student'],
            'general': ['thông tin', 'điểm', 'môn', 'đăng ký', 'sinh viên']
        }

        for intent_name, intent_data in self.intents.items():
            # Tính điểm cho keywords
            keyword_score = self.fuzzy_match_keywords(message, intent_data['keywords'])

            # Tính điểm cho patterns
            pattern_score = self.fuzzy_match_keywords(message, intent_data['patterns'])

            # Tính điểm cho synonyms
            synonym_score = self.fuzzy_match_keywords(message, intent_data['synonyms'])

            # Tổng hợp điểm với trọng số
            total_score = (keyword_score * 0.7 + pattern_score * 0.2 + synonym_score * 0.1)

            # Bonus cho exact match
            normalized_message = self.normalize_text(message)
            for keyword in intent_data['keywords']:
                if self.normalize_text(keyword) in normalized_message:
                    total_score += 0.2
                    break

            # Penalty cho negative keywords
            if intent_name in negative_keywords:
                for neg_keyword in negative_keywords[intent_name]:
                    if self.normalize_text(neg_keyword) in normalized_message:
                        total_score *= 0.3  # Giảm mạnh confidence
                        break

            # Cập nhật best intent nếu điểm cao hơn
            if total_score > best_confidence:
                best_confidence = total_score
                best_intent = intent_name

        # Nếu confidence quá thấp, trả về general
        if best_confidence < self.intent_threshold:
            best_intent = 'general'
            best_confidence = 0.0

        return best_intent, min(best_confidence, 1.0)
    
    def get_student_info(self, user: User, query: str) -> str:
        """Lấy thông tin sinh viên với entity extraction"""
        try:
            entities = self.extract_entities(query)
            analysis = self.analyze_query_intent(query, entities)

            if user.is_staff:
                # Admin có thể tìm kiếm sinh viên khác
                if entities.get('student_id'):
                    student_id = entities['student_id']
                    try:
                        student = Student.objects.get(student_id=student_id)
                        return self.format_student_info(student, analysis)
                    except Student.DoesNotExist:
                        return f"Không tìm thấy sinh viên có mã {student_id}"
                elif analysis['scope'] == 'all':
                    # Trả về danh sách sinh viên
                    students = Student.objects.filter(is_active=True)[:10]
                    if students:
                        result = "Danh sách sinh viên (10 sinh viên đầu):\n\n"
                        for student in students:
                            result += f"• {student.full_name()} ({student.student_id})\n"
                        result += f"\nTổng cộng: {Student.objects.count()} sinh viên"
                        return result
                    return "Không có sinh viên nào trong hệ thống."
                else:
                    return "Vui lòng cung cấp mã sinh viên cụ thể để tìm kiếm thông tin.\nVí dụ: 'thông tin sinh viên 20210001'"
            else:
                # Sinh viên chỉ xem được thông tin của mình
                try:
                    student = Student.objects.get(email=user.email)
                    return self.format_student_info(student, analysis)
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn trong hệ thống."
        except Exception as e:
            logger.error(f"Error getting student info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin sinh viên."

    def format_student_info(self, student: Student, analysis: Dict) -> str:
        """Format thông tin sinh viên theo yêu cầu"""
        target_info = analysis.get('target_info', [])

        # Thông tin cơ bản
        result = f"📋 Thông tin sinh viên {student.full_name()}:\n"
        result += f"🆔 Mã SV: {student.student_id}\n"

        # Thêm thông tin chi tiết nếu được yêu cầu
        if analysis['scope'] == 'detailed' or not target_info:
            result += f"📧 Email: {student.email}\n"
            if student.phone_number:
                result += f"📞 Điện thoại: {student.phone_number}\n"
            if student.address:
                result += f"🏠 Địa chỉ: {student.address}\n"
            result += f"📅 Ngày nhập học: {student.enrollment_date}\n"
            result += f"📊 Trạng thái: {'🟢 Đang học' if student.is_active else '🔴 Đã nghỉ'}\n"

            # Thống kê học tập
            enrollments = Enrollment.objects.filter(student=student)
            total_courses = enrollments.count()
            graded_courses = enrollments.filter(grade__isnull=False).count()
            avg_grade = enrollments.filter(numeric_grade__isnull=False).aggregate(
                avg=Avg('numeric_grade')
            )['avg']

            result += f"\n📚 Thống kê học tập:\n"
            result += f"• Tổng môn đã đăng ký: {total_courses}\n"
            result += f"• Môn đã có điểm: {graded_courses}\n"
            result += f"• Điểm trung bình: {avg_grade:.2f if avg_grade else 'Chưa có'}\n"

        # Thông tin cụ thể theo yêu cầu
        elif target_info:
            if 'diem' in target_info:
                enrollments = Enrollment.objects.filter(student=student)
                avg_grade = enrollments.filter(numeric_grade__isnull=False).aggregate(
                    avg=Avg('numeric_grade')
                )['avg']
                result += f"📊 Điểm trung bình: {avg_grade:.2f if avg_grade else 'Chưa có'}\n"

            if 'ma' in target_info:
                result += f"🆔 Mã sinh viên: {student.student_id}\n"

            if 'ten' in target_info:
                result += f"👤 Họ tên: {student.full_name()}\n"

        return result
    
    def get_course_info(self, query: str) -> str:
        """Lấy thông tin môn học với entity extraction"""
        try:
            entities = self.extract_entities(query)
            analysis = self.analyze_query_intent(query, entities)

            # Tìm kiếm môn học
            courses = None

            if entities.get('course_code'):
                # Tìm theo mã môn học cụ thể
                course_code = entities['course_code']
                courses = Course.objects.filter(course_code__iexact=course_code)
                if not courses:
                    courses = Course.objects.filter(course_code__icontains=course_code)
            else:
                # Tìm theo tên môn học
                search_terms = []
                for word in query.split():
                    if len(word) > 2 and word.lower() not in ['môn', 'học', 'course', 'subject', 'thông', 'tin']:
                        search_terms.append(word)

                if search_terms:
                    q_objects = Q()
                    for term in search_terms:
                        q_objects |= Q(name__icontains=term) | Q(course_code__icontains=term)
                    courses = Course.objects.filter(q_objects)
                else:
                    # Nếu không có từ khóa cụ thể, hiển thị môn học đang hoạt động
                    if analysis['scope'] == 'all':
                        courses = Course.objects.filter(is_active=True)
                    else:
                        courses = Course.objects.filter(is_active=True)[:10]

            # Lọc theo năm và học kỳ nếu có
            if entities.get('year'):
                courses = courses.filter(year=int(entities['year']))
            if entities.get('semester'):
                courses = courses.filter(semester=entities['semester'])

            if courses:
                return self.format_course_info(courses, analysis)
            else:
                if entities.get('course_code'):
                    return f"Không tìm thấy môn học có mã '{entities['course_code']}'"
                else:
                    return "Không tìm thấy môn học nào phù hợp với yêu cầu của bạn.\nVui lòng thử với mã môn học cụ thể, ví dụ: 'môn học IT001'"
        except Exception as e:
            logger.error(f"Error getting course info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin môn học."

    def format_course_info(self, courses, analysis: Dict) -> str:
        """Format thông tin môn học theo yêu cầu"""

        if courses.count() == 1:
            # Hiển thị chi tiết cho 1 môn học
            course = courses.first()
            result = f"📚 Thông tin môn học {course.name}:\n"
            result += f"🆔 Mã môn: {course.course_code}\n"
            result += f"📊 Tín chỉ: {course.credits}\n"
            result += f"📅 Học kỳ: {course.get_semester_display()}\n"
            result += f"🗓️ Năm học: {course.year}\n"
            result += f"⏰ Thời gian: {course.start_date} đến {course.end_date}\n"
            result += f"📝 Trạng thái: {'🟢 Đang mở' if course.is_active else '🔴 Đã đóng'}\n"

            if course.description:
                result += f"📋 Mô tả: {course.description}\n"

            # Thống kê đăng ký
            enrollment_count = Enrollment.objects.filter(course=course).count()
            result += f"👥 Số sinh viên đã đăng ký: {enrollment_count}\n"

        else:
            # Hiển thị danh sách môn học
            result = f"📚 Danh sách môn học ({courses.count()} môn):\n\n"

            for course in courses[:20]:  # Giới hạn 20 môn
                result += f"• {course.name} ({course.course_code})\n"
                if analysis['scope'] == 'detailed':
                    result += f"  📊 {course.credits} tín chỉ | 📅 {course.get_semester_display()} {course.year}\n"
                    result += f"  📝 {'🟢 Đang mở' if course.is_active else '🔴 Đã đóng'}\n"
                result += "\n"

            if courses.count() > 20:
                result += f"... và {courses.count() - 20} môn học khác\n"

        return result
    
    def get_enrollment_info(self, user: User, query: str) -> str:
        """Lấy thông tin đăng ký học với entity extraction"""
        try:
            entities = self.extract_entities(query)
            analysis = self.analyze_query_intent(query, entities)

            if user.is_staff and entities.get('student_id'):
                # Admin xem thông tin đăng ký của sinh viên cụ thể
                try:
                    student = Student.objects.get(student_id=entities['student_id'])
                    return self.format_enrollment_info(student, analysis)
                except Student.DoesNotExist:
                    return f"Không tìm thấy sinh viên có mã {entities['student_id']}"
            elif not user.is_staff:
                # Sinh viên xem thông tin đăng ký của mình
                try:
                    student = Student.objects.get(email=user.email)
                    return self.format_enrollment_info(student, analysis)
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn."
            else:
                return "Vui lòng cung cấp mã sinh viên để xem thông tin đăng ký.\nVí dụ: 'đăng ký của sinh viên 20210001'"
        except Exception as e:
            logger.error(f"Error getting enrollment info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin đăng ký."

    def format_enrollment_info(self, student: Student, analysis: Dict) -> str:
        """Format thông tin đăng ký theo yêu cầu"""
        entities = analysis.get('entities', {})

        enrollments = Enrollment.objects.filter(student=student).order_by('-enrollment_date')

        # Lọc theo năm và học kỳ nếu có
        if entities.get('year'):
            enrollments = enrollments.filter(course__year=int(entities['year']))
        if entities.get('semester'):
            enrollments = enrollments.filter(course__semester=entities['semester'])

        # Lọc theo mã môn học nếu có
        if entities.get('course_code'):
            enrollments = enrollments.filter(course__course_code__icontains=entities['course_code'])

        if not enrollments.exists():
            return f"Sinh viên {student.full_name()} chưa đăng ký môn học nào phù hợp với yêu cầu."

        result = f"📚 Danh sách môn học đã đăng ký - {student.full_name()}:\n\n"

        # Thống kê tổng quan
        total_courses = enrollments.count()
        graded_courses = enrollments.filter(grade__isnull=False).count()
        avg_grade = enrollments.filter(numeric_grade__isnull=False).aggregate(
            avg=Avg('numeric_grade')
        )['avg']

        result += f"📊 Tổng quan:\n"
        result += f"• Tổng môn đã đăng ký: {total_courses}\n"
        result += f"• Môn đã có điểm: {graded_courses}\n"
        avg_grade_str = f"{avg_grade:.2f}" if avg_grade else "Chưa có"
        result += f"• Điểm trung bình: {avg_grade_str}\n\n"

        # Chi tiết từng môn
        result += f"📋 Chi tiết từng môn:\n"
        for i, enrollment in enumerate(enrollments[:20], 1):
            result += f"{i}. {enrollment.course.name} ({enrollment.course.course_code})\n"
            result += f"   📅 Đăng ký: {enrollment.enrollment_date}\n"
            result += f"   📊 Tín chỉ: {enrollment.course.credits}\n"
            result += f"   📚 Học kỳ: {enrollment.course.get_semester_display()} {enrollment.course.year}\n"

            if enrollment.grade:
                result += f"   🎯 Điểm: {enrollment.grade}"
                if enrollment.numeric_grade:
                    result += f" ({enrollment.numeric_grade})"
                result += "\n"
            else:
                result += f"   ⏳ Chưa có điểm\n"
            result += "\n"

        if enrollments.count() > 20:
            result += f"... và {enrollments.count() - 20} môn học khác\n"

        return result

    def get_grade_info(self, user: User, query: str) -> str:
        """Lấy thông tin điểm số với entity extraction"""
        try:
            entities = self.extract_entities(query)
            analysis = self.analyze_query_intent(query, entities)

            if user.is_staff and entities.get('student_id'):
                # Admin xem điểm của sinh viên cụ thể
                try:
                    student = Student.objects.get(student_id=entities['student_id'])
                    return self.format_grade_info(student, analysis)
                except Student.DoesNotExist:
                    return f"Không tìm thấy sinh viên có mã {entities['student_id']}"
            elif not user.is_staff:
                # Sinh viên xem điểm của mình
                try:
                    student = Student.objects.get(email=user.email)
                    return self.format_grade_info(student, analysis)
                except Student.DoesNotExist:
                    return "Không tìm thấy thông tin sinh viên của bạn."
            else:
                return "Vui lòng cung cấp mã sinh viên để xem điểm số.\nVí dụ: 'điểm của sinh viên 20210001'"
        except Exception as e:
            logger.error(f"Error getting grade info: {e}")
            return "Có lỗi xảy ra khi lấy thông tin điểm số."

    def format_grade_info(self, student: Student, analysis: Dict) -> str:
        """Format thông tin điểm số theo yêu cầu"""
        entities = analysis.get('entities', {})

        enrollments = Enrollment.objects.filter(student=student).order_by('-enrollment_date')

        # Lọc theo năm và học kỳ nếu có
        if entities.get('year'):
            enrollments = enrollments.filter(course__year=int(entities['year']))
        if entities.get('semester'):
            enrollments = enrollments.filter(course__semester=entities['semester'])

        # Lọc theo mã môn học nếu có
        if entities.get('course_code'):
            enrollments = enrollments.filter(course__course_code__icontains=entities['course_code'])

        if not enrollments.exists():
            return f"Sinh viên {student.full_name()} chưa có điểm số nào phù hợp với yêu cầu."

        # Tính toán thống kê
        graded_enrollments = enrollments.filter(grade__isnull=False)
        total_courses = enrollments.count()
        graded_courses = graded_enrollments.count()

        avg_grade = graded_enrollments.aggregate(avg=Avg('numeric_grade'))['avg']

        result = f"📊 Kết quả học tập - {student.full_name()}:\n\n"

        # Thống kê tổng quan
        result += f"📈 Tổng quan:\n"
        result += f"• Tổng môn đã đăng ký: {total_courses}\n"
        result += f"• Môn đã có điểm: {graded_courses}\n"
        avg_grade_str = f"{avg_grade:.2f}" if avg_grade else "Chưa có"
        result += f"• Điểm trung bình: {avg_grade_str}\n"

        if avg_grade:
            if avg_grade >= 8.5:
                result += f"• Xếp loại: 🏆 Xuất sắc\n"
            elif avg_grade >= 7.0:
                result += f"• Xếp loại: 🥇 Giỏi\n"
            elif avg_grade >= 5.5:
                result += f"• Xếp loại: 🥈 Khá\n"
            elif avg_grade >= 4.0:
                result += f"• Xếp loại: 🥉 Trung bình\n"
            else:
                result += f"• Xếp loại: ⚠️ Yếu\n"

        result += f"\n📋 Chi tiết điểm từng môn:\n"

        # Hiển thị điểm từng môn
        for i, enrollment in enumerate(graded_enrollments[:15], 1):
            result += f"{i}. {enrollment.course.name} ({enrollment.course.course_code})\n"
            result += f"   📊 Điểm: {enrollment.grade}"
            if enrollment.numeric_grade:
                result += f" ({enrollment.numeric_grade})"
            result += f" | 📚 {enrollment.course.credits} tín chỉ\n"
            result += f"   📅 {enrollment.course.get_semester_display()} {enrollment.course.year}\n\n"

        if graded_enrollments.count() > 15:
            result += f"... và {graded_enrollments.count() - 15} môn khác\n"

        # Môn chưa có điểm
        ungraded = enrollments.filter(grade__isnull=True)
        if ungraded.exists():
            result += f"\n⏳ Môn chưa có điểm ({ungraded.count()}):\n"
            for enrollment in ungraded[:5]:
                result += f"• {enrollment.course.name} ({enrollment.course.course_code})\n"
            if ungraded.count() > 5:
                result += f"... và {ungraded.count() - 5} môn khác\n"

        return result
    
    def search_faq(self, query: str) -> Optional[str]:
        """Tìm kiếm trong FAQ với fuzzy matching"""
        try:
            if not query.strip():
                return None

            # Lấy tất cả FAQ đang hoạt động
            all_faqs = FAQ.objects.filter(is_active=True)

            # Tính điểm similarity cho mỗi FAQ
            faq_scores = []

            for faq in all_faqs:
                # Tính điểm cho question
                question_score = self.calculate_similarity(query, faq.question)

                # Tính điểm cho keywords
                keyword_score = 0.0
                if faq.keywords:
                    keywords = [kw.strip() for kw in faq.keywords.split(',')]
                    keyword_score = self.fuzzy_match_keywords(query, keywords)

                # Tính điểm cho answer (trọng số thấp hơn)
                answer_score = self.calculate_similarity(query, faq.answer) * 0.3

                # Tổng điểm
                total_score = max(question_score, keyword_score) + answer_score

                if total_score > self.similarity_threshold:
                    faq_scores.append((faq, total_score))

            # Sắp xếp theo điểm và lấy top 3
            faq_scores.sort(key=lambda x: x[1], reverse=True)
            top_faqs = faq_scores[:3]

            if top_faqs:
                result = "Tôi tìm thấy những thông tin sau:\n\n"
                for faq, score in top_faqs:
                    result += f"❓ {faq.question}\n"
                    result += f"💡 {faq.answer}\n"
                    result += f"📊 Độ chính xác: {score:.0%}\n\n"
                return result

            return None
        except Exception as e:
            logger.error(f"Error searching FAQ: {e}")
            return None
    
    def generate_response(self, user: User, message: str, intent: str, confidence: float) -> str:
        """Tạo phản hồi dựa trên intent với entity extraction"""

        # Kiểm tra quyền truy cập
        if not user.is_authenticated:
            return "Bạn cần đăng nhập để sử dụng chatbot."

        # Chỉ tìm FAQ khi thực sự không hiểu câu hỏi
        specific_intents = ['student_info', 'course_info', 'enrollment', 'grade', 'schedule']
        if intent == 'general' or (confidence < 0.4 and intent not in specific_intents):
            faq_response = self.search_faq(message)
            if faq_response:
                return faq_response
        
        # Xử lý theo intent với entity extraction
        if intent == 'greeting':
            return f"Xin chào {'quản trị viên' if user.is_staff else 'bạn'}! Tôi là chatbot hỗ trợ hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?"

        elif intent == 'student_info':
            return self.get_student_info(user, message)

        elif intent == 'course_info':
            return self.get_course_info(message)

        elif intent == 'enrollment':
            return self.get_enrollment_info(user, message)

        elif intent == 'grade':
            return self.get_grade_info(user, message)  # Tách riêng logic cho grade
        
        elif intent == 'help':
            help_text = """🤖 Tôi có thể giúp bạn với:

📚 **Thông tin môn học**: Hỏi về môn học, mã môn, tín chỉ
👨‍🎓 **Thông tin sinh viên**: Xem thông tin cá nhân, hồ sơ
📝 **Đăng ký học**: Kiểm tra môn đã đăng ký
📊 **Điểm số**: Xem điểm và kết quả học tập
❓ **Câu hỏi thường gặp**: Các thông tin hữu ích khác

Ví dụ câu hỏi:
- "Thông tin sinh viên của tôi"
- "Môn học IT001"
- "Danh sách môn đã đăng ký"
- "Điểm số của tôi" """
            return help_text
        
        elif intent == 'goodbye':
            return "Cảm ơn bạn đã sử dụng hệ thống! Chúc bạn học tập tốt! 👋"
        
        else:
            return self.generate_fallback_response(message)
    
    def process_message(self, user: User, message: str) -> Dict:
        """Xử lý tin nhắn từ user"""
        start_time = time.time()
        
        try:
            # Lấy hoặc tạo session
            session = self.get_or_create_session(user)
            
            # Lưu tin nhắn của user
            ChatHistory.objects.create(
                session=session,
                message_type='user',
                message=message
            )
            
            # Phát hiện intent
            intent, confidence = self.detect_intent(message)

            # Cải thiện intent với context
            intent, confidence = self.enhance_intent_with_context(message, session, intent, confidence)

            # Tạo phản hồi
            response = self.generate_response(user, message, intent, confidence)
            
            # Tính thời gian phản hồi
            response_time = time.time() - start_time
            
            # Lưu phản hồi của bot
            ChatHistory.objects.create(
                session=session,
                message_type='bot',
                message=response,
                intent=intent,
                confidence=confidence,
                response_time=response_time
            )
            
            return {
                'success': True,
                'response': response,
                'intent': intent,
                'confidence': confidence,
                'response_time': response_time
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                'success': False,
                'response': 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.',
                'error': str(e)
            }
