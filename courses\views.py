from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from .models import Course, Enrollment
from students.models import Student
from quanlysinhvien.mixins import AdminRequiredMixin

class CourseListView(ListView):
    model = Course
    template_name = 'courses/course_list.html'
    context_object_name = 'courses'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                course_code__icontains=search_query
            ) | queryset.filter(
                name__icontains=search_query
            )
        return queryset

class CourseDetailView(DetailView):
    model = Course
    template_name = 'courses/course_detail.html'
    context_object_name = 'course'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['enrollments'] = Enrollment.objects.filter(course=self.object)
        return context

class CourseCreateView(AdminRequiredMixin, CreateView):
    model = Course
    template_name = 'courses/course_form.html'
    fields = ['course_code', 'name', 'description', 'credits', 'semester', 'year', 'start_date', 'end_date', 'is_active']
    success_url = reverse_lazy('course-list')

    def form_valid(self, form):
        messages.success(self.request, 'Môn học đã được tạo thành công!')
        return super().form_valid(form)

class CourseUpdateView(AdminRequiredMixin, UpdateView):
    model = Course
    template_name = 'courses/course_form.html'
    fields = ['course_code', 'name', 'description', 'credits', 'semester', 'year', 'start_date', 'end_date', 'is_active']

    def get_success_url(self):
        return reverse_lazy('course-detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Thông tin môn học đã được cập nhật!')
        return super().form_valid(form)

class CourseDeleteView(AdminRequiredMixin, DeleteView):
    model = Course
    template_name = 'courses/course_confirm_delete.html'
    success_url = reverse_lazy('course-list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Môn học đã được xóa thành công!')
        return super().delete(request, *args, **kwargs)

class EnrollmentListView(ListView):
    model = Enrollment
    template_name = 'courses/enrollment_list.html'
    context_object_name = 'enrollments'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.GET.get('student_id', '')
        course_id = self.request.GET.get('course_id', '')

        if student_id:
            queryset = queryset.filter(student=student_id)
        if course_id:
            queryset = queryset.filter(course=course_id)

        return queryset

    def get_students(self):
        """Return all students for the filter dropdown."""
        return Student.objects.all().order_by('last_name', 'first_name')

    def get_courses(self):
        """Return all courses for the filter dropdown."""
        return Course.objects.all().order_by('name')

class EnrollmentCreateView(AdminRequiredMixin, CreateView):
    model = Enrollment
    template_name = 'courses/enrollment_form.html'
    fields = ['student', 'course']
    success_url = reverse_lazy('enrollment-list')

    def get_initial(self):
        initial = super().get_initial()
        student_id = self.request.GET.get('student_id')
        course_id = self.request.GET.get('course_id')

        if student_id:
            initial['student'] = student_id
        if course_id:
            initial['course'] = course_id

        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.GET.get('student_id'):
            context['student'] = Student.objects.get(id=self.request.GET.get('student_id'))
        if self.request.GET.get('course_id'):
            context['course'] = Course.objects.get(id=self.request.GET.get('course_id'))
        return context

    def form_valid(self, form):
        try:
            messages.success(self.request, 'Đăng ký môn học thành công!')
            return super().form_valid(form)
        except Exception as e:
            messages.error(self.request, f'Lỗi: {str(e)}')
            return super().form_invalid(form)

class EnrollmentUpdateView(AdminRequiredMixin, UpdateView):
    model = Enrollment
    template_name = 'courses/enrollment_form.html'
    fields = ['grade', 'numeric_grade']
    success_url = reverse_lazy('enrollment-list')

    def form_valid(self, form):
        messages.success(self.request, 'Cập nhật điểm thành công!')
        return super().form_valid(form)

class EnrollmentDeleteView(AdminRequiredMixin, DeleteView):
    model = Enrollment
    template_name = 'courses/enrollment_confirm_delete.html'
    success_url = reverse_lazy('enrollment-list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Hủy đăng ký môn học thành công!')
        return super().delete(request, *args, **kwargs)
