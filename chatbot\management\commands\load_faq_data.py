from django.core.management.base import BaseCommand
from chatbot.models import FAQ


class Command(BaseCommand):
    help = 'Load sample FAQ data for chatbot'

    def handle(self, *args, **options):
        # Xóa dữ liệu FAQ cũ
        FAQ.objects.all().delete()
        
        # Dữ liệu FAQ mẫu
        faq_data = [
            # Thông tin chung
            {
                'question': '<PERSON>ệ thống Quản lý Sinh viên là gì?',
                'answer': '<PERSON><PERSON> thống Quản lý Sinh viên là một ứng dụng web giúp quản lý thông tin sinh viên, môn học, đăng ký học và điểm số một cách hiệu quả.',
                'category': 'general',
                'keywords': 'hệ thống, quản lý, sinh viên, giới thiệu'
            },
            {
                'question': 'Làm thế nào để đăng nhập vào hệ thống?',
                'answer': '<PERSON><PERSON><PERSON> có thể đăng nhập bằng cách truy cập trang đăng nhập và nhập username/password được cấp bởi quản trị viên.',
                'category': 'general',
                'keywords': 'đăng nhập, login, username, password'
            },
            {
                'question': 'Tôi quên mật khẩu, phải làm sao?',
                'answer': 'Vui lòng liên hệ với quản trị viên hệ thống để được hỗ trợ reset mật khẩu.',
                'category': 'general',
                'keywords': 'quên mật khẩu, reset password, forgot password'
            },
            
            # Thông tin sinh viên
            {
                'question': 'Làm thế nào để xem thông tin cá nhân?',
                'answer': 'Sinh viên có thể xem thông tin cá nhân bằng cách đăng nhập và truy cập mục "Hồ sơ sinh viên" hoặc hỏi chatbot "thông tin sinh viên của tôi".',
                'category': 'student',
                'keywords': 'thông tin cá nhân, hồ sơ, profile, sinh viên'
            },
            {
                'question': 'Làm thế nào để cập nhật thông tin cá nhân?',
                'answer': 'Hiện tại sinh viên không thể tự cập nhật thông tin. Vui lòng liên hệ phòng đào tạo để được hỗ trợ cập nhật thông tin cá nhân.',
                'category': 'student',
                'keywords': 'cập nhật thông tin, sửa thông tin, update profile'
            },
            {
                'question': 'Mã sinh viên có ý nghĩa gì?',
                'answer': 'Mã sinh viên là định danh duy nhất của mỗi sinh viên trong hệ thống, được sử dụng để tra cứu và quản lý thông tin học tập.',
                'category': 'student',
                'keywords': 'mã sinh viên, student id, định danh'
            },
            
            # Môn học
            {
                'question': 'Làm thế nào để xem danh sách môn học?',
                'answer': 'Bạn có thể xem danh sách môn học trong mục "Môn học" hoặc hỏi chatbot về môn học cụ thể.',
                'category': 'course',
                'keywords': 'danh sách môn học, course list, môn học'
            },
            {
                'question': 'Thông tin môn học bao gồm những gì?',
                'answer': 'Thông tin môn học bao gồm: mã môn học, tên môn học, số tín chỉ, học kỳ, năm học, thời gian bắt đầu và kết thúc.',
                'category': 'course',
                'keywords': 'thông tin môn học, course info, tín chỉ, học kỳ'
            },
            {
                'question': 'Làm thế nào để tìm kiếm môn học?',
                'answer': 'Bạn có thể tìm kiếm môn học theo mã môn học hoặc tên môn học trong hệ thống hoặc hỏi chatbot.',
                'category': 'course',
                'keywords': 'tìm kiếm môn học, search course, mã môn'
            },
            
            # Đăng ký học
            {
                'question': 'Làm thế nào để đăng ký môn học?',
                'answer': 'Hiện tại việc đăng ký môn học được thực hiện bởi quản trị viên. Sinh viên vui lòng liên hệ phòng đào tạo để được hỗ trợ đăng ký.',
                'category': 'enrollment',
                'keywords': 'đăng ký môn học, enrollment, register course'
            },
            {
                'question': 'Làm thế nào để xem danh sách môn đã đăng ký?',
                'answer': 'Sinh viên có thể xem danh sách môn đã đăng ký trong mục "Môn học của tôi" hoặc hỏi chatbot "danh sách môn đã đăng ký".',
                'category': 'enrollment',
                'keywords': 'môn đã đăng ký, enrolled courses, my courses'
            },
            {
                'question': 'Có thể hủy đăng ký môn học không?',
                'answer': 'Việc hủy đăng ký môn học cần được thực hiện bởi quản trị viên. Vui lòng liên hệ phòng đào tạo để được hỗ trợ.',
                'category': 'enrollment',
                'keywords': 'hủy đăng ký, cancel enrollment, withdraw'
            },
            
            # Điểm số
            {
                'question': 'Làm thế nào để xem điểm số?',
                'answer': 'Sinh viên có thể xem điểm số trong mục "Môn học của tôi" hoặc hỏi chatbot "điểm số của tôi".',
                'category': 'grade',
                'keywords': 'xem điểm, grade, điểm số, kết quả học tập'
            },
            {
                'question': 'Hệ thống tính điểm như thế nào?',
                'answer': 'Hệ thống sử dụng thang điểm chữ (A+, A, B+, B, C+, C, D+, D, F) và điểm số tương ứng để đánh giá kết quả học tập.',
                'category': 'grade',
                'keywords': 'thang điểm, grading scale, điểm chữ, điểm số'
            },
            {
                'question': 'Làm thế nào để tính điểm trung bình?',
                'answer': 'Điểm trung bình được tính dựa trên điểm số của các môn học đã có điểm, có trọng số theo số tín chỉ của từng môn.',
                'category': 'grade',
                'keywords': 'điểm trung bình, GPA, average grade'
            },
            
            # Hệ thống
            {
                'question': 'Hệ thống có những chức năng gì?',
                'answer': 'Hệ thống bao gồm các chức năng: quản lý thông tin sinh viên, quản lý môn học, đăng ký học, quản lý điểm số, thống kê báo cáo và chatbot hỗ trợ.',
                'category': 'system',
                'keywords': 'chức năng, features, tính năng'
            },
            {
                'question': 'Ai có thể sử dụng hệ thống?',
                'answer': 'Hệ thống có 2 loại người dùng: Quản trị viên (có quyền quản lý toàn bộ) và Sinh viên (chỉ xem được thông tin của mình).',
                'category': 'system',
                'keywords': 'người dùng, user, quyền hạn, permission'
            },
            {
                'question': 'Làm thế nào để liên hệ hỗ trợ?',
                'answer': 'Bạn có thể sử dụng chatbot này để được hỗ trợ nhanh chóng, hoặc liên hệ trực tiếp với phòng đào tạo để được hỗ trợ chi tiết hơn.',
                'category': 'system',
                'keywords': 'liên hệ, support, hỗ trợ, help'
            }
        ]
        
        # Tạo dữ liệu FAQ
        created_count = 0
        for faq_item in faq_data:
            faq, created = FAQ.objects.get_or_create(
                question=faq_item['question'],
                defaults={
                    'answer': faq_item['answer'],
                    'category': faq_item['category'],
                    'keywords': faq_item['keywords'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully loaded {created_count} FAQ items'
            )
        )
