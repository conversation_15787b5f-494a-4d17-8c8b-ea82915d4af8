{% extends 'base.html' %}

{% block title %}Thống kê - <PERSON><PERSON> thống <PERSON>ản lý Sin<PERSON> viên{% endblock %}

{% block extra_css %}
<style>
    .stat-card {
        transition: transform 0.3s;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-chart-line me-2"></i>Thống kê</h1>
    </div>

    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <a href="{% url 'student-list' %}" class="text-decoration-none">
                <div class="card stat-card clickable-card h-100 border-primary">
                    <div class="card-body">
                        <h5 class="card-title text-primary"><PERSON><PERSON> viên</h5>
                        <div class="row">
                            <div class="col-7">
                                <canvas id="studentChart" width="100" height="100"></canvas>
                            </div>
                            <div class="col-5">
                                <div class="d-flex flex-column justify-content-center h-100">
                                    <p class="mb-1">Tổng số: <strong>{{ total_students }}</strong></p>
                                    <p class="mb-1">Đang học: <strong>{{ active_students }}</strong></p>
                                    <p class="mb-1">Đã nghỉ: <strong>{{ inactive_students }}</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-primary">
                        <small class="text-primary">
                            <i class="fas fa-arrow-right me-1"></i>Nhấn để xem danh sách sinh viên
                        </small>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-3">
            <a href="{% url 'course-list' %}" class="text-decoration-none">
                <div class="card stat-card clickable-card h-100 border-success">
                    <div class="card-body">
                        <h5 class="card-title text-success">Môn học</h5>
                        <div class="row">
                            <div class="col-7">
                                <canvas id="courseChart" width="100" height="100"></canvas>
                            </div>
                            <div class="col-5">
                                <div class="d-flex flex-column justify-content-center h-100">
                                    <p class="mb-1">Tổng số: <strong>{{ total_courses }}</strong></p>
                                    <p class="mb-1">Đang mở: <strong>{{ active_courses }}</strong></p>
                                    <p class="mb-1">Đã đóng: <strong>{{ inactive_courses }}</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-success">
                        <small class="text-success">
                            <i class="fas fa-arrow-right me-1"></i>Nhấn để xem danh sách môn học
                        </small>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-3">
            <a href="{% url 'enrollment-list' %}" class="text-decoration-none">
                <div class="card stat-card clickable-card h-100 border-info">
                    <div class="card-body">
                        <h5 class="card-title text-info">Đăng ký học</h5>
                        <div class="text-center">
                            <div class="display-4 mb-2">{{ total_enrollments }}</div>
                            <p class="mb-1">Tổng số đăng ký</p>
                            {% if avg_grade.avg %}
                            <div class="mt-3">
                                <h6>Điểm trung bình: <span class="badge bg-info">{{ avg_grade.avg|floatformat:2 }}</span></h6>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-info">
                        <small class="text-info">
                            <i class="fas fa-arrow-right me-1"></i>Nhấn để xem danh sách đăng ký
                        </small>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Popular Courses -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Môn học phổ biến</h5>
                </div>
                <div class="card-body">
                    {% if courses_by_enrollment %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Mã môn học</th>
                                    <th>Tên môn học</th>
                                    <th>Số tín chỉ</th>
                                    <th>Số sinh viên đăng ký</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for course in courses_by_enrollment %}
                                <tr>
                                    <td>{{ course.course_code }}</td>
                                    <td>
                                        <a href="{% url 'course-detail' course.id %}">
                                            {{ course.name }}
                                        </a>
                                    </td>
                                    <td>{{ course.credits }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ course.enrollment_count }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-center">Chưa có dữ liệu.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Student Chart
    const studentCtx = document.getElementById('studentChart').getContext('2d');
    const studentChart = new Chart(studentCtx, {
        type: 'doughnut',
        data: {
            labels: ['Đang học', 'Đã nghỉ'],
            datasets: [{
                data: [{{ active_students }}, {{ inactive_students }}],
                backgroundColor: ['#0d6efd', '#6c757d'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Course Chart
    const courseCtx = document.getElementById('courseChart').getContext('2d');
    const courseChart = new Chart(courseCtx, {
        type: 'doughnut',
        data: {
            labels: ['Đang mở', 'Đã đóng'],
            datasets: [{
                data: [{{ active_courses }}, {{ inactive_courses }}],
                backgroundColor: ['#198754', '#6c757d'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
</script>
{% endblock %}
