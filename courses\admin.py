from django.contrib import admin
from .models import Course, Enrollment

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('course_code', 'name', 'credits', 'semester', 'year', 'is_active')
    list_filter = ('is_active', 'semester', 'year', 'credits')
    search_fields = ('course_code', 'name', 'description')
    fieldsets = (
        ('Thông tin môn học', {
            'fields': ('course_code', 'name', 'description', 'credits')
        }),
        ('Thời gian', {
            'fields': ('semester', 'year', 'start_date', 'end_date')
        }),
        ('Trạng thái', {
            'fields': ('is_active',)
        }),
    )

@admin.register(Enrollment)
class EnrollmentAdmin(admin.ModelAdmin):
    list_display = ('student', 'course', 'enrollment_date', 'grade', 'numeric_grade')
    list_filter = ('course', 'enrollment_date', 'grade')
    search_fields = ('student__student_id', 'student__first_name', 'student__last_name', 'course__name', 'course__course_code')
    fieldsets = (
        ('Thông tin đăng ký', {
            'fields': ('student', 'course', 'enrollment_date')
        }),
        ('Điểm số', {
            'fields': ('grade', 'numeric_grade')
        }),
    )
    readonly_fields = ('enrollment_date',)
