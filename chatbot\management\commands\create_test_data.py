from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import models
from students.models import Student
from courses.models import Course, Enrollment
from datetime import date


class Command(BaseCommand):
    help = 'Tạo dữ liệu demo cho hệ thống'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Tạo dữ liệu demo cho hệ thống...'))
        
        # Tạo test user
        test_user, created = User.objects.get_or_create(
            username='test_student',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON><PERSON>ễ<PERSON>',
                'last_name': 'Văn Test',
                'is_staff': False
            }
        )
        
        if created:
            test_user.set_password('password123')
            test_user.save()
            self.stdout.write(f"✅ Tạo user: {test_user.username}")
        else:
            self.stdout.write(f"ℹ️  User đã tồn tại: {test_user.username}")
        
        # Tạo test student
        test_student, created = Student.objects.get_or_create(
            student_id='TEST001',
            defaults={
                'first_name': 'Văn Test',
                'last_name': '<PERSON><PERSON><PERSON>n',
                'date_of_birth': date(2000, 1, 15),
                'gender': 'M',
                'email': '<EMAIL>',
                'phone_number': '0123456789',
                'address': '123 Đường Test, TP.HCM',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f"✅ Tạo student: {test_student.student_id}")
        else:
            self.stdout.write(f"ℹ️  Student đã tồn tại: {test_student.student_id}")
        
        # Tạo test courses
        courses_data = [
            {
                'course_code': 'IT001',
                'name': 'Nhập môn Lập trình',
                'credits': 3,
                'semester': '1',
                'year': 2024,
                'start_date': date(2024, 1, 15),
                'end_date': date(2024, 5, 15),
                'description': 'Môn học cơ bản về lập trình với Python'
            },
            {
                'course_code': 'IT002',
                'name': 'Cấu trúc Dữ liệu',
                'credits': 4,
                'semester': '1',
                'year': 2024,
                'start_date': date(2024, 1, 15),
                'end_date': date(2024, 5, 15),
                'description': 'Học về các cấu trúc dữ liệu cơ bản'
            },
            {
                'course_code': 'MATH101',
                'name': 'Toán Cao cấp A1',
                'credits': 3,
                'semester': '1',
                'year': 2024,
                'start_date': date(2024, 1, 15),
                'end_date': date(2024, 5, 15),
                'description': 'Toán cao cấp dành cho sinh viên IT'
            },
            {
                'course_code': 'ENG101',
                'name': 'Tiếng Anh 1',
                'credits': 2,
                'semester': '2',
                'year': 2024,
                'start_date': date(2024, 6, 1),
                'end_date': date(2024, 10, 1),
                'description': 'Tiếng Anh cơ bản'
            },
            {
                'course_code': 'IT003',
                'name': 'Cơ sở Dữ liệu',
                'credits': 4,
                'semester': '2',
                'year': 2024,
                'start_date': date(2024, 6, 1),
                'end_date': date(2024, 10, 1),
                'description': 'Thiết kế và quản lý cơ sở dữ liệu'
            }
        ]
        
        created_courses = []
        for course_data in courses_data:
            course, created = Course.objects.get_or_create(
                course_code=course_data['course_code'],
                defaults=course_data
            )
            created_courses.append(course)
            
            if created:
                self.stdout.write(f"✅ Tạo course: {course.course_code} - {course.name}")
            else:
                self.stdout.write(f"ℹ️  Course đã tồn tại: {course.course_code}")
        
        # Tạo enrollments với điểm
        enrollments_data = [
            {
                'course_code': 'IT001',
                'grade': 'A',
                'numeric_grade': 8.5,
                'enrollment_date': date(2024, 1, 10)
            },
            {
                'course_code': 'IT002',
                'grade': 'B+',
                'numeric_grade': 7.8,
                'enrollment_date': date(2024, 1, 10)
            },
            {
                'course_code': 'MATH101',
                'grade': 'A+',
                'numeric_grade': 9.2,
                'enrollment_date': date(2024, 1, 10)
            },
            {
                'course_code': 'ENG101',
                'grade': 'B',
                'numeric_grade': 7.0,
                'enrollment_date': date(2024, 6, 1)
            },
            {
                'course_code': 'IT003',
                'grade': None,  # Chưa có điểm
                'numeric_grade': None,
                'enrollment_date': date(2024, 6, 1)
            }
        ]
        
        for enrollment_data in enrollments_data:
            course = Course.objects.get(course_code=enrollment_data['course_code'])
            enrollment, created = Enrollment.objects.get_or_create(
                student=test_student,
                course=course,
                defaults={
                    'grade': enrollment_data['grade'],
                    'numeric_grade': enrollment_data['numeric_grade'],
                    'enrollment_date': enrollment_data['enrollment_date']
                }
            )
            
            if created:
                grade_info = f" - Điểm: {enrollment_data['grade']}" if enrollment_data['grade'] else " - Chưa có điểm"
                self.stdout.write(f"✅ Tạo enrollment: {course.course_code}{grade_info}")
            else:
                self.stdout.write(f"ℹ️  Enrollment đã tồn tại: {course.course_code}")
        
        # Tạo admin user để test
        admin_user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'Test',
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(f"✅ Tạo admin: {admin_user.username}")
        else:
            self.stdout.write(f"ℹ️  Admin đã tồn tại: {admin_user.username}")
        
        # Thống kê
        self.stdout.write(f"\n📊 Thống kê dữ liệu test:")
        self.stdout.write(f"👤 Test Student: {test_student.student_id} - {test_student.full_name()}")
        self.stdout.write(f"📚 Courses: {len(created_courses)}")
        self.stdout.write(f"📝 Enrollments: {Enrollment.objects.filter(student=test_student).count()}")
        
        avg_grade = Enrollment.objects.filter(
            student=test_student,
            numeric_grade__isnull=False
        ).aggregate(avg=models.Avg('numeric_grade'))['avg']

        avg_grade_str = f"{avg_grade:.2f}" if avg_grade else "N/A"
        self.stdout.write(f"📊 Điểm TB: {avg_grade_str}")
        
        self.stdout.write(f"\n🔑 Login credentials:")
        self.stdout.write(f"Student: test_student / password123")
        self.stdout.write(f"Admin: test_admin / admin123")
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Tạo dữ liệu demo thành công!'))
