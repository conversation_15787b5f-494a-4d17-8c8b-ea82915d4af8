# HƯỚNG DẪN SỬ DỤNG CHATBOT HỆ THỐNG QUẢN LÝ SINH VIÊN

## Giới thiệu

Chatbot là trợ lý ảo được tích hợp vào hệ thống Quản lý <PERSON> viên, giúp người dùng tìm kiếm thông tin và giải đáp thắc mắc một cách nhanh chóng và tiện lợi.

## Tính năng chính

### 1. T<PERSON><PERSON> cập Chatbot
- **Widget Chatbot**: Hiển thị ở góc phải dưới mỗi trang (chỉ khi đã đăng nhập)
- **Trang Chat**: Truy cập trực tiếp tại `/chatbot/`
- **Y<PERSON>u cầu**: Phải đăng nhập để sử dụng

### 2. <PERSON><PERSON><PERSON> loại câu hỏi được hỗ trợ

#### Thông tin sinh viên
- "Thông tin sinh viên của tôi"
- "<PERSON><PERSON> sơ cá nhân"
- "Mã sinh viên [mã_sv]" (chỉ admin)

#### Môn học
- "Thông tin môn học [tên/mã môn]"
- "Danh sách môn học"
- "Môn học IT001"

#### Đăng ký học
- "Danh sách môn đã đăng ký"
- "Môn học của tôi"
- "Lịch sử đăng ký"

#### Điểm số
- "Điểm số của tôi"
- "Kết quả học tập"
- "Điểm trung bình"

#### Hỗ trợ
- "Help" hoặc "Hướng dẫn"
- "Giúp đỡ"

### 3. Phân quyền

#### Sinh viên
- Xem thông tin cá nhân của mình
- Xem môn học đã đăng ký
- Xem điểm số của mình
- Truy cập FAQ và thông tin chung

#### Quản trị viên (Admin)
- Tất cả quyền của sinh viên
- Tìm kiếm thông tin sinh viên khác
- Truy cập đầy đủ thông tin hệ thống

## Cách sử dụng

### 1. Sử dụng Widget Chatbot
1. Đăng nhập vào hệ thống
2. Click vào biểu tượng chat ở góc phải dưới
3. Nhập câu hỏi vào ô tin nhắn
4. Nhấn Enter hoặc click nút gửi

### 2. Sử dụng trang Chat
1. Truy cập `/chatbot/`
2. Sử dụng các nút gợi ý hoặc nhập câu hỏi tự do
3. Click "Gửi" để gửi tin nhắn

### 3. Tính năng bổ sung
- **Lịch sử chat**: Tự động lưu và hiển thị lại khi truy cập
- **Xóa lịch sử**: Click nút "Xóa lịch sử" để bắt đầu cuộc trò chuyện mới
- **Gợi ý câu hỏi**: Sử dụng các nút gợi ý để hỏi nhanh

## Ví dụ câu hỏi

### Câu hỏi cơ bản
```
- Xin chào
- Thông tin sinh viên của tôi
- Danh sách môn đã đăng ký
- Điểm số của tôi
- Help
```

### Câu hỏi nâng cao
```
- Môn học Toán cao cấp
- Thông tin môn IT001
- Điểm trung bình của tôi
- Làm thế nào để đăng ký môn học?
- Hệ thống tính điểm như thế nào?
```

## Giới hạn và lưu ý

### Giới hạn sử dụng
- **Rate limiting**: Tối đa 30 tin nhắn/phút cho mỗi người dùng
- **Độ dài tin nhắn**: Tối đa 500 ký tự
- **Phiên làm việc**: Tự động tạo mới khi cần thiết

### Lưu ý bảo mật
- Chỉ người dùng đã đăng nhập mới có thể sử dụng
- Sinh viên chỉ xem được thông tin của mình
- Admin có thể truy cập thông tin của tất cả sinh viên
- Tất cả hoạt động được ghi log để kiểm tra

## Xử lý lỗi

### Lỗi thường gặp
1. **"Bạn cần đăng nhập để sử dụng chatbot"**
   - Giải pháp: Đăng nhập vào hệ thống

2. **"Quá nhiều yêu cầu. Vui lòng thử lại sau"**
   - Giải pháp: Chờ 1 phút rồi thử lại

3. **"Không thể kết nối đến server"**
   - Giải pháp: Kiểm tra kết nối mạng và thử lại

4. **"Xin lỗi, tôi chưa hiểu câu hỏi của bạn"**
   - Giải pháp: Thử diễn đạt lại câu hỏi hoặc sử dụng từ khóa đơn giản hơn

## Quản lý FAQ (Dành cho Admin)

### Truy cập Admin Panel
1. Đăng nhập với tài khoản admin
2. Truy cập `/admin/`
3. Vào mục "Chatbot" > "Câu hỏi thường gặp"

### Quản lý FAQ
- **Thêm FAQ mới**: Click "Add FAQ"
- **Chỉnh sửa**: Click vào câu hỏi cần sửa
- **Xóa**: Chọn và delete
- **Phân loại**: Sử dụng category để nhóm câu hỏi
- **Từ khóa**: Thêm keywords để tăng khả năng tìm kiếm

### Theo dõi hoạt động
- **Chat History**: Xem lịch sử chat của người dùng
- **Chat Session**: Quản lý phiên chat
- **Log files**: Kiểm tra file log tại `logs/chatbot.log`

## Kỹ thuật

### Công nghệ sử dụng
- **Backend**: Django, Python
- **NLP**: PhoBERT (dự kiến), Intent Recognition
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **Database**: SQLite (có thể chuyển sang PostgreSQL)

### Cấu trúc dữ liệu
- **FAQ**: Câu hỏi thường gặp
- **ChatSession**: Phiên chat của người dùng
- **ChatHistory**: Lịch sử tin nhắn

### API Endpoints
- `POST /chatbot/api/message/`: Gửi tin nhắn
- `GET /chatbot/api/history/`: Lấy lịch sử chat
- `POST /chatbot/api/clear/`: Xóa lịch sử chat

## Hỗ trợ

Nếu gặp vấn đề khi sử dụng chatbot, vui lòng:
1. Thử các câu hỏi gợi ý
2. Kiểm tra kết nối mạng
3. Liên hệ quản trị viên hệ thống
4. Báo cáo lỗi qua email hoặc hệ thống ticket

---

*Tài liệu này được cập nhật lần cuối: 14/07/2025*
