from django.contrib import admin
from .models import FAQ, ChatSession, ChatHistory


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    list_display = ['question_short', 'category', 'is_active', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['question', 'answer', 'keywords']
    list_editable = ['is_active']
    ordering = ['-created_at']

    def question_short(self, obj):
        return obj.question[:50] + "..." if len(obj.question) > 50 else obj.question
    question_short.short_description = "Câu hỏi"


@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'session_id_short', 'started_at', 'last_activity', 'is_active']
    list_filter = ['is_active', 'started_at', 'last_activity']
    search_fields = ['user__username', 'session_id']
    readonly_fields = ['session_id', 'started_at']
    ordering = ['-last_activity']

    def session_id_short(self, obj):
        return obj.session_id[:8] + "..."
    session_id_short.short_description = "Session ID"


@admin.register(ChatHistory)
class ChatHistoryAdmin(admin.ModelAdmin):
    list_display = ['session_user', 'message_type', 'message_short', 'intent', 'confidence', 'timestamp']
    list_filter = ['message_type', 'intent', 'timestamp']
    search_fields = ['message', 'session__user__username']
    readonly_fields = ['timestamp']
    ordering = ['-timestamp']

    def session_user(self, obj):
        return obj.session.user.username
    session_user.short_description = "Người dùng"

    def message_short(self, obj):
        return obj.message[:30] + "..." if len(obj.message) > 30 else obj.message
    message_short.short_description = "Tin nhắn"
