from django.db import models
from students.models import Student

class Course(models.Model):
    SEMESTER_CHOICES = [
        ('1', '<PERSON><PERSON><PERSON> kỳ 1'),
        ('2', '<PERSON><PERSON><PERSON> kỳ 2'),
        ('3', '<PERSON><PERSON><PERSON> kỳ hè'),
    ]

    course_code = models.CharField(max_length=10, unique=True, verbose_name="<PERSON><PERSON> môn học")
    name = models.CharField(max_length=100, verbose_name="Tên môn học")
    description = models.TextField(blank=True, null=True, verbose_name="<PERSON>ô tả")
    credits = models.PositiveSmallIntegerField(verbose_name="Số tín chỉ")
    semester = models.CharField(max_length=1, choices=SEMESTER_CHOICES, verbose_name="<PERSON><PERSON><PERSON> kỳ")
    year = models.PositiveSmallIntegerField(verbose_name="Năm học")
    start_date = models.DateField(verbose_name="<PERSON><PERSON><PERSON> bắt đầu")
    end_date = models.DateField(verbose_name="<PERSON><PERSON><PERSON> kết thúc")
    is_active = models.BooleanField(default=True, verbose_name="<PERSON><PERSON> mở")

    class Meta:
        verbose_name = "<PERSON>ô<PERSON> học"
        verbose_name_plural = "Môn học"
        ordering = ['year', 'semester', 'name']

    def __str__(self):
        return f"{self.name} ({self.course_code})"

class Enrollment(models.Model):
    GRADE_CHOICES = [
        ('A+', 'A+'),
        ('A', 'A'),
        ('B+', 'B+'),
        ('B', 'B'),
        ('C+', 'C+'),
        ('C', 'C'),
        ('D+', 'D+'),
        ('D', 'D'),
        ('F', 'F'),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='enrollments', verbose_name="Sinh viên")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrollments', verbose_name="Môn học")
    enrollment_date = models.DateField(auto_now_add=True, verbose_name="Ngày đăng ký")
    grade = models.CharField(max_length=2, choices=GRADE_CHOICES, blank=True, null=True, verbose_name="Điểm chữ")
    numeric_grade = models.DecimalField(max_digits=4, decimal_places=2, blank=True, null=True, verbose_name="Điểm số")

    class Meta:
        verbose_name = "Đăng ký môn học"
        verbose_name_plural = "Đăng ký môn học"
        unique_together = ['student', 'course']
        ordering = ['-enrollment_date']

    def __str__(self):
        return f"{self.student} - {self.course}"
