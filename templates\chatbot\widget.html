{% load static %}

<!-- Chatbot Widget -->
<div id="chatbot-widget" class="chatbot-widget">
    <!-- Chat Toggle Button -->
    <div id="chat-toggle" class="chat-toggle">
        <i class="fas fa-comments"></i>
        <span class="chat-badge" id="chat-badge" style="display: none;">1</span>
    </div>
    
    <!-- Chat Window -->
    <div id="chat-window" class="chat-window" style="display: none;">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="chat-title">
                <i class="fas fa-robot me-2"></i>
                Trợ lý ảo
            </div>
            <div class="chat-controls">
                <button id="chat-clear" class="btn-chat-control" title="Xóa lịch sử">
                    <i class="fas fa-trash"></i>
                </button>
                <button id="chat-minimize" class="btn-chat-control" title="Thu nhỏ">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div id="chat-messages" class="chat-messages">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">
                        Xin chào! Tôi là trợ lý ảo của hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?
                    </div>
                    <div class="message-time">
                        <small class="text-muted">Vừa xong</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="chat-input">
            <div class="input-group">
                <input type="text" id="chat-message-input" class="form-control" 
                       placeholder="Nhập tin nhắn..." maxlength="500">
                <button id="chat-send" class="btn btn-primary" type="button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div id="chat-typing" class="chat-typing" style="display: none;">
                <small class="text-muted">
                    <i class="fas fa-circle-notch fa-spin"></i>
                    Đang trả lời...
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Chatbot Styles -->
<style>
.chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Roboto', sans-serif;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.chat-toggle i {
    color: white;
    font-size: 24px;
}

.chat-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.chat-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-title {
    font-weight: 600;
    font-size: 16px;
}

.chat-controls {
    display: flex;
    gap: 8px;
}

.btn-chat-control {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.btn-chat-control:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: #007bff;
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #28a745;
    color: white;
    margin-right: 0;
    margin-left: 10px;
}

.message-content {
    flex: 1;
    max-width: 80%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: white;
    padding: 10px 12px;
    border-radius: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.user-message .message-text {
    background: #007bff;
    color: white;
}

.message-time {
    margin-top: 5px;
}

.chat-input {
    padding: 15px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-typing {
    padding: 5px 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .chat-window {
        width: 300px;
        height: 400px;
    }
    
    .chatbot-widget {
        bottom: 15px;
        right: 15px;
    }
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<!-- Chatbot JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatToggle = document.getElementById('chat-toggle');
    const chatWindow = document.getElementById('chat-window');
    const chatMinimize = document.getElementById('chat-minimize');
    const chatClear = document.getElementById('chat-clear');
    const chatMessages = document.getElementById('chat-messages');
    const chatInput = document.getElementById('chat-message-input');
    const chatSend = document.getElementById('chat-send');
    const chatTyping = document.getElementById('chat-typing');
    
    let isOpen = false;
    
    // Toggle chat window
    chatToggle.addEventListener('click', function() {
        isOpen = !isOpen;
        chatWindow.style.display = isOpen ? 'flex' : 'none';
        
        if (isOpen) {
            chatInput.focus();
            loadChatHistory();
        }
    });
    
    // Minimize chat
    chatMinimize.addEventListener('click', function() {
        isOpen = false;
        chatWindow.style.display = 'none';
    });
    
    // Clear chat
    chatClear.addEventListener('click', function() {
        if (confirm('Bạn có chắc muốn xóa lịch sử chat?')) {
            clearChat();
        }
    });
    
    // Send message
    chatSend.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    function sendMessage() {
        const message = chatInput.value.trim();
        if (!message) return;
        
        // Add user message to chat
        addMessage('user', message);
        chatInput.value = '';
        
        // Show typing indicator
        showTyping(true);
        
        // Send to server
        fetch('/chatbot/api/message/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            showTyping(false);
            if (data.success) {
                addMessage('bot', data.response);
            } else {
                addMessage('bot', 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.');
            }
        })
        .catch(error => {
            showTyping(false);
            addMessage('bot', 'Không thể kết nối đến server. Vui lòng thử lại.');
        });
    }
    
    function addMessage(type, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = type === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        messageText.textContent = text;
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.innerHTML = '<small class="text-muted">Vừa xong</small>';
        
        content.appendChild(messageText);
        content.appendChild(messageTime);
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function showTyping(show) {
        chatTyping.style.display = show ? 'block' : 'none';
    }
    
    function loadChatHistory() {
        fetch('/chatbot/api/history/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.messages.length > 1) {
                // Clear default message
                chatMessages.innerHTML = '';
                
                data.messages.forEach(msg => {
                    addMessage(msg.type, msg.message);
                });
            }
        })
        .catch(error => {
            console.error('Error loading chat history:', error);
        });
    }
    
    function clearChat() {
        fetch('/chatbot/api/clear/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                chatMessages.innerHTML = `
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                Xin chào! Tôi là trợ lý ảo của hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?
                            </div>
                            <div class="message-time">
                                <small class="text-muted">Vừa xong</small>
                            </div>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error clearing chat:', error);
        });
    }
    
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});
</script>
