# 🚀 Production Deployment Guide

## 📋 **Pre-deployment Checklist**

### ✅ **System Requirements**
- [ ] Python 3.8+ installed
- [ ] PostgreSQL 12+ (recommended) or MySQL 8+
- [ ] Redis 6+ (for caching and sessions)
- [ ] Nginx (web server)
- [ ] SSL certificate
- [ ] Domain name configured

### ✅ **Security Checklist**
- [ ] Environment variables configured
- [ ] Secret key generated
- [ ] Database credentials secured
- [ ] SSL/TLS certificate installed
- [ ] Firewall configured
- [ ] Backup strategy implemented

---

## 🐧 **Ubuntu/Debian Deployment**

### 1. **System Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib redis-server nginx git

# Install Node.js (for frontend assets)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

### 2. **Database Setup**
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE quanlysinhvien;
CREATE USER quanlysinhvien_user WITH PASSWORD 'your_secure_password';
ALTER ROLE quanlysinhvien_user SET client_encoding TO 'utf8';
ALTER ROLE quanlysinhvien_user SET default_transaction_isolation TO 'read committed';
ALTER ROLE quanlysinhvien_user SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE quanlysinhvien TO quanlysinhvien_user;
\q
```

### 3. **Application Setup**
```bash
# Create application user
sudo adduser --system --group quanlysinhvien

# Create application directory
sudo mkdir -p /var/www/quanlysinhvien
sudo chown quanlysinhvien:quanlysinhvien /var/www/quanlysinhvien

# Switch to application user
sudo -u quanlysinhvien -s

# Clone repository
cd /var/www/quanlysinhvien
git clone https://github.com/your-repo/QuanLySinhVien.git .

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn psycopg2-binary
```

### 4. **Environment Configuration**
```bash
# Create environment file
sudo -u quanlysinhvien nano /var/www/quanlysinhvien/.env
```

```env
# .env file content
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database
DB_NAME=quanlysinhvien
DB_USER=quanlysinhvien_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_URL=redis://127.0.0.1:6379/1

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### 5. **Django Setup**
```bash
# Activate virtual environment
source /var/www/quanlysinhvien/venv/bin/activate

# Set Django settings
export DJANGO_SETTINGS_MODULE=quanlysinhvien.settings_production

# Run migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic --noinput

# Create superuser
python manage.py createsuperuser

# Load initial data
python manage.py load_faq_data
python manage.py create_test_data
```

### 6. **Gunicorn Configuration**
```bash
# Create Gunicorn configuration
sudo nano /var/www/quanlysinhvien/gunicorn.conf.py
```

```python
# gunicorn.conf.py
bind = "127.0.0.1:8000"
workers = 3
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
user = "quanlysinhvien"
group = "quanlysinhvien"
tmp_upload_dir = None
errorlog = "/var/www/quanlysinhvien/logs/gunicorn_error.log"
accesslog = "/var/www/quanlysinhvien/logs/gunicorn_access.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
```

### 7. **Systemd Service**
```bash
# Create systemd service
sudo nano /etc/systemd/system/quanlysinhvien.service
```

```ini
[Unit]
Description=QuanLySinhVien Django Application
After=network.target postgresql.service redis.service

[Service]
Type=notify
User=quanlysinhvien
Group=quanlysinhvien
WorkingDirectory=/var/www/quanlysinhvien
Environment=DJANGO_SETTINGS_MODULE=quanlysinhvien.settings_production
EnvironmentFile=/var/www/quanlysinhvien/.env
ExecStart=/var/www/quanlysinhvien/venv/bin/gunicorn quanlysinhvien.wsgi:application -c /var/www/quanlysinhvien/gunicorn.conf.py
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable quanlysinhvien
sudo systemctl start quanlysinhvien
sudo systemctl status quanlysinhvien
```

### 8. **Nginx Configuration**
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/quanlysinhvien
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    client_max_body_size 5M;

    location /static/ {
        alias /var/www/quanlysinhvien/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /var/www/quanlysinhvien/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/quanlysinhvien /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🐳 **Docker Deployment**

### 1. **Dockerfile**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Collect static files
RUN python manage.py collectstatic --noinput --settings=quanlysinhvien.settings_production

EXPOSE 8000

CMD ["gunicorn", "quanlysinhvien.wsgi:application", "--bind", "0.0.0.0:8000"]
```

### 2. **Docker Compose**
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=quanlysinhvien.settings_production
    env_file:
      - .env
    depends_on:
      - db
      - redis
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: quanlysinhvien
      POSTGRES_USER: quanlysinhvien_user
      POSTGRES_PASSWORD: your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./staticfiles:/var/www/static
      - ./media:/var/www/media
    depends_on:
      - web

volumes:
  postgres_data:
  redis_data:
```

---

## 🔧 **Maintenance & Monitoring**

### 📊 **Health Checks**
```bash
# Check application status
sudo systemctl status quanlysinhvien

# Check logs
sudo journalctl -u quanlysinhvien -f

# Check Nginx status
sudo systemctl status nginx

# Check database connection
sudo -u postgres psql -d quanlysinhvien -c "SELECT 1;"

# Check Redis
redis-cli ping
```

### 🔄 **Updates & Deployment**
```bash
# Update application
cd /var/www/quanlysinhvien
sudo -u quanlysinhvien git pull origin main
sudo -u quanlysinhvien source venv/bin/activate
sudo -u quanlysinhvien pip install -r requirements.txt
sudo -u quanlysinhvien python manage.py migrate
sudo -u quanlysinhvien python manage.py collectstatic --noinput
sudo systemctl restart quanlysinhvien
```

### 💾 **Backup Strategy**
```bash
# Database backup
sudo -u postgres pg_dump quanlysinhvien > backup_$(date +%Y%m%d_%H%M%S).sql

# Media files backup
tar -czf media_backup_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/quanlysinhvien/media/

# Automated backup script
#!/bin/bash
BACKUP_DIR="/var/backups/quanlysinhvien"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Database backup
sudo -u postgres pg_dump quanlysinhvien | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Media backup
tar -czf $BACKUP_DIR/media_$DATE.tar.gz /var/www/quanlysinhvien/media/

# Keep only last 7 days
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

---

## 🚨 **Troubleshooting**

### Common Issues
1. **502 Bad Gateway**: Check Gunicorn service status
2. **Static files not loading**: Run `collectstatic` command
3. **Database connection error**: Check PostgreSQL service and credentials
4. **Permission denied**: Check file ownership and permissions
5. **SSL certificate issues**: Verify certificate paths and validity

### Log Locations
- **Application logs**: `/var/www/quanlysinhvien/logs/`
- **Nginx logs**: `/var/log/nginx/`
- **System logs**: `journalctl -u quanlysinhvien`
- **PostgreSQL logs**: `/var/log/postgresql/`

---

## 📈 **Performance Optimization**

### Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_student_email ON students_student(email);
CREATE INDEX idx_enrollment_student ON courses_enrollment(student_id);
CREATE INDEX idx_enrollment_course ON courses_enrollment(course_id);
CREATE INDEX idx_chat_history_user ON chatbot_chathistory(user_id);
```

### Caching Configuration
```python
# Redis caching for sessions and cache
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

---

## ✅ **Post-Deployment Verification**

1. **Functionality Tests**
   - [ ] User registration and login
   - [ ] Student management CRUD operations
   - [ ] Course management functionality
   - [ ] Chatbot responses
   - [ ] File uploads
   - [ ] Email notifications

2. **Performance Tests**
   - [ ] Page load times < 2 seconds
   - [ ] Chatbot response time < 100ms
   - [ ] Database query performance
   - [ ] Static file serving

3. **Security Tests**
   - [ ] HTTPS redirect working
   - [ ] Security headers present
   - [ ] Authentication required for protected pages
   - [ ] Rate limiting functional
   - [ ] File upload restrictions

4. **Monitoring Setup**
   - [ ] Error tracking configured
   - [ ] Performance monitoring active
   - [ ] Uptime monitoring enabled
   - [ ] Backup automation working
