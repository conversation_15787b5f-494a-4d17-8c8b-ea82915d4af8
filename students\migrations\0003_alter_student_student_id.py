# Generated by Django 5.2.1 on 2025-05-27 09:01

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0002_alter_student_student_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='student',
            name='student_id',
            field=models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^[A-Za-z0-9]+$', '<PERSON><PERSON> sinh viên chỉ được chứa chữ cái và số, không có ký tự đặc biệt.')], verbose_name='Mã sinh viên'),
        ),
    ]
