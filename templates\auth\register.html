{% extends 'base.html' %}

{% block title %}<PERSON><PERSON><PERSON> ký - <PERSON><PERSON> thống <PERSON> l<PERSON> viên{% endblock %}

{% block extra_css %}
<style>
    .register-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0;
    }
    
    .register-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .role-selector {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .role-option {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .role-option:hover {
        background: #e9ecef;
    }
    
    .role-option input[type="radio"] {
        margin-right: 10px;
    }
    
    .role-icon {
        font-size: 1.5rem;
        margin-right: 10px;
        width: 30px;
        text-align: center;
    }
    
    .password-strength {
        margin-top: 5px;
        font-size: 0.875rem;
    }
    
    .strength-weak { color: #dc3545; }
    .strength-medium { color: #fd7e14; }
    .strength-strong { color: #198754; }
</style>
{% endblock %}

{% block content %}
<div class="register-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card register-card shadow-lg">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>Đăng ký tài khoản
                        </h3>
                        <p class="mb-0 mt-2">Tạo tài khoản mới để sử dụng hệ thống</p>
                    </div>
                    <div class="card-body p-4">
                        <form method="post" novalidate>
                            {% csrf_token %}
                            
                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Có lỗi xảy ra:</strong>
                                <ul class="mb-0 mt-2">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                        <li>{{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                            
                            <!-- Role Selection -->
                            <div class="role-selector">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-user-tag me-2"></i>Chọn vai trò
                                </label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="role-option">
                                            <input type="radio" name="role" value="admin" id="role_admin" 
                                                   {% if form.role.value == 'admin' %}checked{% endif %}>
                                            <div class="role-icon text-danger">
                                                <i class="fas fa-user-shield"></i>
                                            </div>
                                            <div>
                                                <strong>Quản trị viên</strong>
                                                <small class="d-block text-muted">Toàn quyền quản lý hệ thống</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="role-option">
                                            <input type="radio" name="role" value="student" id="role_student" 
                                                   {% if form.role.value == 'student' or not form.role.value %}checked{% endif %}>
                                            <div class="role-icon text-primary">
                                                <i class="fas fa-user-graduate"></i>
                                            </div>
                                            <div>
                                                <strong>Sinh viên</strong>
                                                <small class="d-block text-muted">Xem thông tin và đăng ký học</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Personal Information -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        {{ form.last_name }}
                                    </div>
                                    {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        {{ form.first_name }}
                                    </div>
                                    {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Account Information -->
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">{{ form.username.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-at"></i></span>
                                    {{ form.username }}
                                </div>
                                {% if form.username.help_text %}
                                <small class="form-text text-muted">{{ form.username.help_text }}</small>
                                {% endif %}
                                {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    {{ form.email }}
                                </div>
                                {% if form.email.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <!-- Password -->
                            <div class="mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">{{ form.password1.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    {{ form.password1 }}
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword1">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="passwordStrength"></div>
                                {% if form.password1.help_text %}
                                <small class="form-text text-muted">{{ form.password1.help_text }}</small>
                                {% endif %}
                                {% if form.password1.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password1.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-4">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">{{ form.password2.label }}</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    {{ form.password2 }}
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword2">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                {% if form.password2.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password2.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>Tạo tài khoản
                                </button>
                            </div>
                            
                            <div class="text-center">
                                <p class="mb-0">Đã có tài khoản? 
                                    <a href="{% url 'login' %}" class="text-decoration-none">
                                        <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập ngay
                                    </a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    function togglePasswordVisibility(toggleBtn, passwordField) {
        toggleBtn.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }
    
    const togglePassword1 = document.getElementById('togglePassword1');
    const togglePassword2 = document.getElementById('togglePassword2');
    const password1 = document.getElementById('{{ form.password1.id_for_label }}');
    const password2 = document.getElementById('{{ form.password2.id_for_label }}');
    
    if (togglePassword1 && password1) {
        togglePasswordVisibility(togglePassword1, password1);
    }
    
    if (togglePassword2 && password2) {
        togglePasswordVisibility(togglePassword2, password2);
    }
    
    // Password strength checker
    if (password1) {
        password1.addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.innerHTML = '';
                return;
            }
            
            let strength = 0;
            let feedback = [];
            
            // Length check
            if (password.length >= 8) strength++;
            else feedback.push('ít nhất 8 ký tự');
            
            // Uppercase check
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('chữ hoa');
            
            // Lowercase check
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('chữ thường');
            
            // Number check
            if (/\d/.test(password)) strength++;
            else feedback.push('số');
            
            // Special character check
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
            else feedback.push('ký tự đặc biệt');
            
            let strengthText = '';
            let strengthClass = '';
            
            if (strength < 2) {
                strengthText = 'Yếu';
                strengthClass = 'strength-weak';
            } else if (strength < 4) {
                strengthText = 'Trung bình';
                strengthClass = 'strength-medium';
            } else {
                strengthText = 'Mạnh';
                strengthClass = 'strength-strong';
            }
            
            let feedbackText = feedback.length > 0 ? ` (Thiếu: ${feedback.join(', ')})` : '';
            strengthDiv.innerHTML = `<span class="${strengthClass}">Độ mạnh: ${strengthText}${feedbackText}</span>`;
        });
    }
    
    // Role selection styling
    const roleOptions = document.querySelectorAll('.role-option');
    roleOptions.forEach(option => {
        option.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Update styling
            roleOptions.forEach(opt => opt.classList.remove('bg-light'));
            this.classList.add('bg-light');
        });
    });
    
    // Initialize role selection styling
    const checkedRole = document.querySelector('.role-option input[type="radio"]:checked');
    if (checkedRole) {
        checkedRole.closest('.role-option').classList.add('bg-light');
    }
});
</script>
{% endblock %}
