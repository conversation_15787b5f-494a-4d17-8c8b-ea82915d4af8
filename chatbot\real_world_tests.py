"""
Test cases thực tế - <PERSON>á<PERSON> câu hỏi mà người dùng thường hỏi
"""

REAL_WORLD_TEST_CASES = [
    # Câu hỏi về thông tin sinh viên
    {
        'question': 'Tôi muốn xem thông tin cá nhân',
        'expected_intent': 'student_info',
        'expected_content': ['mã sv', 'họ tên', 'email']
    },
    {
        'question': 'Cho tôi biết mã sinh viên của tôi',
        'expected_intent': 'student_info', 
        'expected_content': ['mã sv']
    },
    {
        'question': 'Tôi là sinh viên nào?',
        'expected_intent': 'student_info',
        'expected_content': ['họ tên', 'mã sv']
    },
    
    # Câu hỏi về môn học cụ thể
    {
        'question': 'Môn IT001 là gì?',
        'expected_intent': 'course_info',
        'expected_content': ['IT001', 'tên môn', 'tín chỉ']
    },
    {
        'question': 'Thông tin chi tiết về môn Toán cao cấp',
        'expected_intent': 'course_info',
        'expected_content': ['toán cao cấp', 'tín chỉ']
    },
    {
        'question': 'Có môn học nào về lập trình không?',
        'expected_intent': 'course_info',
        'expected_content': ['lập trình']
    },
    
    # Câu hỏi về đăng ký
    {
        'question': 'Tôi đã đăng ký những môn nào?',
        'expected_intent': 'enrollment',
        'expected_content': ['đăng ký', 'danh sách']
    },
    {
        'question': 'Xem danh sách môn học của tôi',
        'expected_intent': 'enrollment',
        'expected_content': ['danh sách', 'môn học']
    },
    {
        'question': 'Tôi có bao nhiêu môn?',
        'expected_intent': 'enrollment',
        'expected_content': ['số môn', 'tổng']
    },
    
    # Câu hỏi về điểm
    {
        'question': 'Điểm của tôi như thế nào?',
        'expected_intent': 'grade',
        'expected_content': ['điểm', 'kết quả']
    },
    {
        'question': 'Tôi được bao nhiêu điểm trung bình?',
        'expected_intent': 'grade',
        'expected_content': ['điểm trung bình', 'GPA']
    },
    {
        'question': 'Kết quả học tập của tôi',
        'expected_intent': 'grade',
        'expected_content': ['kết quả', 'điểm']
    },
    
    # Câu hỏi phức tạp
    {
        'question': 'Tôi muốn biết thông tin chi tiết về sinh viên 20210001',
        'expected_intent': 'student_info',
        'expected_content': ['20210001', 'thông tin']
    },
    {
        'question': 'Môn IT001 có bao nhiêu tín chỉ và học kỳ nào?',
        'expected_intent': 'course_info',
        'expected_content': ['IT001', 'tín chỉ', 'học kỳ']
    },
    {
        'question': 'Danh sách môn đã đăng ký học kỳ 1 năm 2024',
        'expected_intent': 'enrollment',
        'expected_content': ['đăng ký', '2024', 'học kỳ 1']
    },
    
    # Câu hỏi mơ hồ
    {
        'question': 'Tôi muốn xem',
        'expected_intent': 'general',
        'expected_content': ['gợi ý', 'help']
    },
    {
        'question': 'Cho tôi biết',
        'expected_intent': 'general', 
        'expected_content': ['gợi ý', 'help']
    },
    {
        'question': 'Tôi cần',
        'expected_intent': 'general',
        'expected_content': ['gợi ý', 'help']
    },
    
    # Câu hỏi với typo
    {
        'question': 'thong tin sinh vien cua toi',
        'expected_intent': 'student_info',
        'expected_content': ['thông tin', 'sinh viên']
    },
    {
        'question': 'diem so cua toi',
        'expected_intent': 'grade',
        'expected_content': ['điểm']
    },
    {
        'question': 'mon hoc da dang ky',
        'expected_intent': 'enrollment',
        'expected_content': ['môn học', 'đăng ký']
    },
    
    # Câu hỏi về hệ thống
    {
        'question': 'Hệ thống này có những tính năng gì?',
        'expected_intent': 'help',
        'expected_content': ['tính năng', 'hệ thống']
    },
    {
        'question': 'Tôi có thể hỏi gì?',
        'expected_intent': 'help',
        'expected_content': ['có thể hỏi', 'gợi ý']
    },
    {
        'question': 'Làm sao để sử dụng?',
        'expected_intent': 'help',
        'expected_content': ['sử dụng', 'hướng dẫn']
    },
    
    # Câu hỏi context-dependent
    {
        'question': 'Chi tiết hơn',
        'expected_intent': 'general',
        'expected_content': ['context', 'gợi ý']
    },
    {
        'question': 'Xem thêm',
        'expected_intent': 'general',
        'expected_content': ['context', 'gợi ý']
    },
    {
        'question': 'Còn gì nữa?',
        'expected_intent': 'general',
        'expected_content': ['context', 'gợi ý']
    },
    
    # Câu hỏi về thời gian
    {
        'question': 'Lịch học của tôi',
        'expected_intent': 'schedule',
        'expected_content': ['lịch học', 'thời gian']
    },
    {
        'question': 'Môn nào học hôm nay?',
        'expected_intent': 'schedule',
        'expected_content': ['hôm nay', 'lịch']
    },
    
    # Câu hỏi kết hợp
    {
        'question': 'Thông tin và điểm của sinh viên 20210001',
        'expected_intent': 'student_info',
        'expected_content': ['20210001', 'thông tin', 'điểm']
    },
    {
        'question': 'Môn học và điểm của tôi',
        'expected_intent': 'enrollment',
        'expected_content': ['môn học', 'điểm']
    }
]

def get_real_world_tests():
    """Lấy tất cả test cases thực tế"""
    return REAL_WORLD_TEST_CASES

def get_tests_by_intent(intent):
    """Lấy test cases theo intent"""
    return [test for test in REAL_WORLD_TEST_CASES if test['expected_intent'] == intent]

def get_complex_tests():
    """Lấy các test cases phức tạp"""
    complex_keywords = ['chi tiết', 'bao nhiêu', 'như thế nào', 'có', 'nào', 'gì']
    return [test for test in REAL_WORLD_TEST_CASES 
            if any(keyword in test['question'].lower() for keyword in complex_keywords)]
