# Hệ thống Quản lý Sinh viên

<PERSON>ệ thống quản lý sinh viên đư<PERSON> phát triển bằng Djan<PERSON>, cho phép quản lý thông tin sinh viên, môn học và đăng ký học.

## Tính năng chính

- 👥 **Quản lý sinh viên**: <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>, x<PERSON><PERSON>, tì<PERSON> kiếm sinh viên
- 📚 **Quản lý môn học**: Quản lý thông tin môn học và lịch học
- 📝 **Đăng ký học**: Đăng ký/hủy đăng ký môn học cho sinh viên
- 📊 **Thống kê báo cáo**: Dashboard với các thống kê chi tiết
- 🔐 **Phân quyền**: <PERSON>ệ thống phân quyền cho admin và sinh viên

## Y<PERSON><PERSON> c<PERSON><PERSON> h<PERSON> thống

- Python 3.8+
- Django 5.0+
- Pillow (x<PERSON> lý hình ảnh)

## Cài đặt

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd QuanLySinhVien
   ```

2. **Tạo môi trường ảo**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Cài đặt dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Cấu hình database**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

5. **Tạo superuser**
   ```bash
   python manage.py createsuperuser
   ```

6. **Chạy server**
   ```bash
   python manage.py runserver
   ```

## Sử dụng

- **Trang chủ**: http://127.0.0.1:8000/
- **Admin panel**: http://127.0.0.1:8000/admin/
- **Đăng nhập**: http://127.0.0.1:8000/login/

### Tài khoản mặc định
- **Username**: admin
- **Password**: admin123

## Cấu trúc project

```
QuanLySinhVien/
├── docs/                    # Documentation
├── quanlysinhvien/         # Main Django project
├── students/               # Student management app
├── courses/                # Course management app
├── templates/              # HTML templates
├── static/                 # Static files (CSS, JS, images)
├── media/                  # User uploaded files
├── requirements.txt        # Python dependencies
└── manage.py              # Django management script
```

## Phân quyền

### Admin
- Toàn quyền quản lý hệ thống
- Truy cập admin panel
- Quản lý sinh viên, môn học, đăng ký
- Xem thống kê và báo cáo

### Sinh viên
- Xem thông tin cá nhân
- Đăng ký/hủy đăng ký môn học
- Xem điểm số

## Đóng góp

1. Fork project
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## License

Distributed under the MIT License. See `LICENSE` for more information.

## Liên hệ

- Email: <EMAIL>
- Website: https://example.com/support

---

© 2025 Hệ thống Quản lý Sinh viên
