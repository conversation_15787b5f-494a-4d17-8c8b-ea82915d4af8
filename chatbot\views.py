import json
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import timedelta
from quanlysinhvien.decorators import admin_required
from .services import ChatbotService
from .models import ChatHistory, ChatSession, FAQ


@method_decorator(login_required, name='dispatch')
class ChatView(View):
    """View xử lý chat interface"""

    def get(self, request):
        """Hiển thị giao diện chat (nếu cần)"""
        return render(request, 'chatbot/chat.html')


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def chat_message(request):
    """API endpoint xử lý tin nhắn chat"""
    try:
        data = json.loads(request.body)
        message = data.get('message', '').strip()

        if not message:
            return JsonResponse({
                'success': False,
                'error': 'Tin nhắn không được để trống'
            }, status=400)

        # Khởi tạo chatbot service
        chatbot = ChatbotService()

        # Xử lý tin nhắn
        result = chatbot.process_message(request.user, message)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Dữ liệu JSON không hợp lệ'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Có lỗi xảy ra khi xử lý tin nhắn'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def chat_history(request):
    """API endpoint lấy lịch sử chat"""
    try:
        # Lấy session hiện tại của user
        session = ChatSession.objects.filter(
            user=request.user,
            is_active=True
        ).first()

        if not session:
            return JsonResponse({
                'success': True,
                'messages': []
            })

        # Lấy lịch sử tin nhắn
        messages = ChatHistory.objects.filter(
            session=session
        ).order_by('timestamp')

        message_list = []
        for msg in messages:
            message_list.append({
                'type': msg.message_type,
                'message': msg.message,
                'timestamp': msg.timestamp.isoformat(),
                'intent': msg.intent,
                'confidence': msg.confidence
            })

        return JsonResponse({
            'success': True,
            'messages': message_list
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Có lỗi xảy ra khi lấy lịch sử chat'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def clear_chat(request):
    """API endpoint xóa lịch sử chat"""
    try:
        # Đánh dấu session hiện tại là không hoạt động
        ChatSession.objects.filter(
            user=request.user,
            is_active=True
        ).update(is_active=False)

        return JsonResponse({
            'success': True,
            'message': 'Đã xóa lịch sử chat'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Có lỗi xảy ra khi xóa lịch sử chat'
        }, status=500)


@admin_required
def chatbot_stats(request):
    """View thống kê chatbot cho admin"""
    try:
        # Thống kê tổng quan
        total_sessions = ChatSession.objects.count()
        active_sessions = ChatSession.objects.filter(is_active=True).count()
        total_messages = ChatHistory.objects.count()
        total_faqs = FAQ.objects.filter(is_active=True).count()

        # Thống kê theo thời gian (7 ngày qua)
        week_ago = timezone.now() - timedelta(days=7)
        recent_sessions = ChatSession.objects.filter(started_at__gte=week_ago).count()
        recent_messages = ChatHistory.objects.filter(timestamp__gte=week_ago).count()

        # Thống kê intent phổ biến
        popular_intents = ChatHistory.objects.filter(
            intent__isnull=False,
            timestamp__gte=week_ago
        ).values('intent').annotate(
            count=Count('intent')
        ).order_by('-count')[:10]

        # Thống kê thời gian phản hồi trung bình
        avg_response_time = ChatHistory.objects.filter(
            response_time__isnull=False,
            timestamp__gte=week_ago
        ).aggregate(avg_time=Avg('response_time'))['avg_time']

        # Thống kê người dùng hoạt động
        active_users = ChatSession.objects.filter(
            last_activity__gte=week_ago
        ).values('user__username').distinct().count()

        context = {
            'total_sessions': total_sessions,
            'active_sessions': active_sessions,
            'total_messages': total_messages,
            'total_faqs': total_faqs,
            'recent_sessions': recent_sessions,
            'recent_messages': recent_messages,
            'popular_intents': popular_intents,
            'avg_response_time': round(avg_response_time, 3) if avg_response_time else 0,
            'active_users': active_users,
        }

        return render(request, 'chatbot/stats.html', context)

    except Exception as e:
        return render(request, 'chatbot/stats.html', {
            'error': 'Có lỗi xảy ra khi tải thống kê'
        })
