import json
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from .services import ChatbotService
from .models import ChatHistory, ChatSession


@method_decorator(login_required, name='dispatch')
class ChatView(View):
    """View xử lý chat interface"""

    def get(self, request):
        """Hiển thị giao diện chat (nếu cần)"""
        return render(request, 'chatbot/chat.html')


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def chat_message(request):
    """API endpoint xử lý tin nhắn chat"""
    try:
        data = json.loads(request.body)
        message = data.get('message', '').strip()

        if not message:
            return JsonResponse({
                'success': False,
                'error': 'Tin nhắn không được để trống'
            }, status=400)

        # Khởi tạo chatbot service
        chatbot = ChatbotService()

        # Xử lý tin nhắn
        result = chatbot.process_message(request.user, message)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Dữ liệu JSON không hợp lệ'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Có lỗi xảy ra khi xử lý tin nhắn'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def chat_history(request):
    """API endpoint lấy lịch sử chat"""
    try:
        # Lấy session hiện tại của user
        session = ChatSession.objects.filter(
            user=request.user,
            is_active=True
        ).first()

        if not session:
            return JsonResponse({
                'success': True,
                'messages': []
            })

        # Lấy lịch sử tin nhắn
        messages = ChatHistory.objects.filter(
            session=session
        ).order_by('timestamp')

        message_list = []
        for msg in messages:
            message_list.append({
                'type': msg.message_type,
                'message': msg.message,
                'timestamp': msg.timestamp.isoformat(),
                'intent': msg.intent,
                'confidence': msg.confidence
            })

        return JsonResponse({
            'success': True,
            'messages': message_list
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Có lỗi xảy ra khi lấy lịch sử chat'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def clear_chat(request):
    """API endpoint xóa lịch sử chat"""
    try:
        # Đánh dấu session hiện tại là không hoạt động
        ChatSession.objects.filter(
            user=request.user,
            is_active=True
        ).update(is_active=False)

        return JsonResponse({
            'success': True,
            'message': 'Đã xóa lịch sử chat'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'Có lỗi xảy ra khi xóa lịch sử chat'
        }, status=500)
