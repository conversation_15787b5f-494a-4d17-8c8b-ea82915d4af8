# Generated by Django 5.2.4 on 2025-07-14 07:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.TextField(verbose_name='Câu hỏi')),
                ('answer', models.TextField(verbose_name='Câu trả lời')),
                ('category', models.CharField(choices=[('general', 'Thông tin chung'), ('student', 'Thông tin sinh viên'), ('course', 'Môn học'), ('enrollment', 'Đăng ký học'), ('grade', 'Điểm số'), ('system', '<PERSON><PERSON> thống')], default='general', max_length=20, verbose_name='<PERSON><PERSON> mục')),
                ('keywords', models.TextField(blank=True, help_text='Từ khóa liên quan, cách nhau bởi dấu phẩy', null=True, verbose_name='Từ khóa')),
                ('is_active', models.BooleanField(default=True, verbose_name='Kích hoạt')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Ngày tạo')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Ngày cập nhật')),
            ],
            options={
                'verbose_name': 'Câu hỏi thường gặp',
                'verbose_name_plural': 'Câu hỏi thường gặp',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100, unique=True, verbose_name='ID phiên')),
                ('started_at', models.DateTimeField(auto_now_add=True, verbose_name='Bắt đầu')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='Hoạt động cuối')),
                ('is_active', models.BooleanField(default=True, verbose_name='Đang hoạt động')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_sessions', to=settings.AUTH_USER_MODEL, verbose_name='Người dùng')),
            ],
            options={
                'verbose_name': 'Phiên chat',
                'verbose_name_plural': 'Phiên chat',
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='ChatHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('user', 'Người dùng'), ('bot', 'Bot')], max_length=10, verbose_name='Loại tin nhắn')),
                ('message', models.TextField(verbose_name='Tin nhắn')),
                ('intent', models.CharField(blank=True, max_length=50, null=True, verbose_name='Ý định')),
                ('confidence', models.FloatField(blank=True, null=True, verbose_name='Độ tin cậy')),
                ('response_time', models.FloatField(blank=True, help_text='Thời gian phản hồi (giây)', null=True, verbose_name='Thời gian phản hồi')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='Thời gian')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='chatbot.chatsession', verbose_name='Phiên chat')),
            ],
            options={
                'verbose_name': 'Lịch sử chat',
                'verbose_name_plural': 'Lịch sử chat',
                'ordering': ['timestamp'],
            },
        ),
    ]
