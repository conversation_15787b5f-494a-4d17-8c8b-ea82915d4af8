{% extends 'base.html' %}

{% block title %}
    {% if form.instance.pk %}Chỉnh sửa{% else %}Thêm mới{% endif %} Sinh viên - <PERSON>ệ thống Quản lý Sinh viên
{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'student-list' %}">Sinh viên</a></li>
            <li class="breadcrumb-item active">
                {% if form.instance.pk %}Chỉnh sửa{% else %}Thêm mới{% endif %} Sinh viên
            </li>
        </ol>
    </nav>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-{% if form.instance.pk %}edit{% else %}plus{% endif %} me-2"></i>
                {% if form.instance.pk %}Chỉnh sửa{% else %}Thêm mới{% endif %} Sinh viên
            </h5>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" novalidate>
                {% csrf_token %}

                {% if form.errors %}
                <div class="alert alert-danger">
                    <strong>Lỗi:</strong> Vui lòng kiểm tra lại thông tin.
                </div>
                {% endif %}

                <div class="row">
                    <!-- Thông tin cá nhân -->
                    <div class="col-md-6">
                        <h5 class="mb-3 text-primary">Thông tin cá nhân</h5>

                        <div class="mb-3">
                            <label for="{{ form.student_id.id_for_label }}" class="form-label">{{ form.student_id.label }}</label>
                            <input type="text" name="{{ form.student_id.name }}" id="{{ form.student_id.id_for_label }}"
                                   class="form-control {% if form.student_id.errors %}is-invalid{% endif %}"
                                   value="{{ form.student_id.value|default:'' }}"
                                   maxlength="20"
                                   placeholder="Ví dụ: SV001, 2021001, ABC123..."
                                   required>
                            <div class="student-id-counter" id="student-id-counter">
                                <span id="char-count">0</span>/20 ký tự
                            </div>
                            {% if form.student_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.student_id.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Mã sinh viên có thể chứa chữ cái và số, tối đa 20 ký tự. Ví dụ: SV001, 2021001, ABC123
                            </small>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }}</label>
                                <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}"
                                       class="form-control {% if form.last_name.errors %}is-invalid{% endif %}"
                                       value="{{ form.last_name.value|default:'' }}" required>
                                {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }}</label>
                                <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}"
                                       class="form-control {% if form.first_name.errors %}is-invalid{% endif %}"
                                       value="{{ form.first_name.value|default:'' }}" required>
                                {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">{{ form.date_of_birth.label }}</label>
                            <div class="date-input-wrapper">
                                <input type="date" name="{{ form.date_of_birth.name }}" id="{{ form.date_of_birth.id_for_label }}"
                                       class="form-control {% if form.date_of_birth.errors %}is-invalid{% endif %}"
                                       value="{{ form.date_of_birth.value|date:'Y-m-d'|default:'' }}"
                                       max="{% now 'Y-m-d' %}"
                                       min="1950-01-01"
                                       required>
                            </div>
                            {% if form.date_of_birth.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.date_of_birth.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">Chọn ngày sinh của sinh viên</small>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.gender.id_for_label }}" class="form-label">{{ form.gender.label }}</label>
                            <select name="{{ form.gender.name }}" id="{{ form.gender.id_for_label }}"
                                    class="form-select {% if form.gender.errors %}is-invalid{% endif %}" required>
                                <option value="" {% if not form.gender.value %}selected{% endif %}>-- Chọn giới tính --</option>
                                {% for choice in form.gender.field.choices %}
                                <option value="{{ choice.0 }}" {% if form.gender.value == choice.0|stringformat:"s" %}selected{% endif %}>
                                    {{ choice.1 }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if form.gender.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.gender.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.photo.id_for_label }}" class="form-label">{{ form.photo.label }}</label>
                            <input type="file" name="{{ form.photo.name }}" id="{{ form.photo.id_for_label }}"
                                   class="form-control {% if form.photo.errors %}is-invalid{% endif %}">
                            {% if form.photo.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.photo.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                            {% if form.instance.photo %}
                            <div class="mt-2">
                                <img src="{{ form.instance.photo.url }}" alt="Current photo" class="img-thumbnail" style="max-height: 100px;">
                                <small class="text-muted d-block">Ảnh hiện tại</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Thông tin liên hệ và học tập -->
                    <div class="col-md-6">
                        <h5 class="mb-3 text-primary">Thông tin liên hệ</h5>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                            <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}"
                                   class="form-control {% if form.email.errors %}is-invalid{% endif %}"
                                   value="{{ form.email.value|default:'' }}" required>
                            {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }}</label>
                            <input type="tel" name="{{ form.phone_number.name }}" id="{{ form.phone_number.id_for_label }}"
                                   class="form-control {% if form.phone_number.errors %}is-invalid{% endif %}"
                                   value="{{ form.phone_number.value|default:'' }}" required>
                            {% if form.phone_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone_number.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                            <textarea name="{{ form.address.name }}" id="{{ form.address.id_for_label }}"
                                      class="form-control {% if form.address.errors %}is-invalid{% endif %}"
                                      rows="3">{{ form.address.value|default:'' }}</textarea>
                            {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <h5 class="mb-3 mt-4 text-primary">Thông tin học tập</h5>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="{{ form.is_active.name }}"
                                       id="{{ form.is_active.id_for_label }}"
                                       {% if form.is_active.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                            <div class="text-danger">
                                {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{% if form.instance.pk %}{% url 'student-detail' form.instance.pk %}{% else %}{% url 'student-list' %}{% endif %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Hủy
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Lưu
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
