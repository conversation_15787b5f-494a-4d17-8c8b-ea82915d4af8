=====================================================
HỆ THỐNG QUẢN LÝ SINH VIÊN - HƯỚNG DẪN TRIỂN KHAI VÀ SỬ DỤNG
=====================================================

I. GIỚI THIỆU
-------------
Hệ thống Quản lý Sinh viên là một ứng dụng web được phát triển bằng Django, cho phép quản lý thông tin sinh viên, môn học và đăng ký học. Hệ thống sử dụng cơ sở dữ liệu SQLite để lưu trữ dữ liệu.

Các chức năng chính:
- Quản lý thông tin sinh viên (thêm, sửa, xóa, xem chi tiết)
- Quản lý môn học (thêm, sử<PERSON>, x<PERSON><PERSON>, xem chi tiết)
- Quản lý đăng ký học và điểm số
- Thống kê và báo cáo
- Hệ thống xác thực người dùng

II. YÊU CẦU HỆ THỐNG
--------------------
- Python 3.8 trở lên
- Django 5.0 trở lên
- Pillow (để xử lý hình ảnh)
- Trình duyệt web hiện đại (Chrome, Firefox, Edge, Safari)

III. TRIỂN KHAI HỆ THỐNG
------------------------

1. Cài đặt Python và môi trường ảo
----------------------------------
a) Cài đặt Python:
   - Tải và cài đặt Python từ https://www.python.org/downloads/
   - Đảm bảo đã chọn "Add Python to PATH" trong quá trình cài đặt

b) Tạo và kích hoạt môi trường ảo:
   - Mở Command Prompt hoặc PowerShell
   - Di chuyển đến thư mục dự án:
     cd đường_dẫn_đến_thư_mục_dự_án

   - Tạo môi trường ảo:
     python -m venv venv

   - Kích hoạt môi trường ảo:
     + Windows (Command Prompt): venv\Scripts\activate.bat
     + Windows (PowerShell): venv\Scripts\Activate.ps1
     + Linux/Mac: source venv/bin/activate

2. Cài đặt các gói phụ thuộc
----------------------------
Với môi trường ảo đã được kích hoạt, chạy lệnh sau để cài đặt các gói cần thiết:

   pip install django pillow

3. Cấu hình cơ sở dữ liệu
-------------------------
a) Tạo các bảng trong cơ sở dữ liệu:
   python manage.py makemigrations
   python manage.py migrate

b) Tạo tài khoản quản trị viên:
   python manage.py createsuperuser
   (Làm theo hướng dẫn để tạo tên người dùng, email và mật khẩu)

   Hoặc sử dụng tài khoản mặc định đã được tạo:
   - Tên đăng nhập: admin
   - Mật khẩu: admin123

4. Chạy máy chủ phát triển
--------------------------
   python manage.py runserver

   Sau khi chạy lệnh này, hệ thống sẽ khả dụng tại: http://127.0.0.1:8000/

IV. HƯỚNG DẪN SỬ DỤNG
---------------------

1. Đăng ký/Đăng nhập/Đăng xuất hệ thống
---------------------------------------
a) Đăng ký tài khoản mới:
- Truy cập http://127.0.0.1:8000/register/ để đăng ký tài khoản mới
- Điền đầy đủ thông tin: họ tên, tên đăng nhập, email, mật khẩu
- Chọn vai trò: Quản trị viên hoặc Sinh viên
- Nhấp "Tạo tài khoản" để hoàn tất đăng ký
- Hệ thống sẽ tự động đăng nhập sau khi đăng ký thành công

Phân quyền theo vai trò:
- Quản trị viên: Toàn quyền quản lý hệ thống, truy cập admin panel
- Sinh viên: Xem thông tin cá nhân, đăng ký môn học, xem điểm

b) Đăng nhập:
- Truy cập http://127.0.0.1:8000/login/ để đăng nhập vào hệ thống
- Truy cập http://127.0.0.1:8000/admin/ để đăng nhập vào giao diện quản trị
- Sử dụng tài khoản đã đăng ký hoặc tài khoản mặc định

Tài khoản mặc định:
- Tên đăng nhập: admin
- Mật khẩu: admin123
- Vai trò: Quản trị viên

c) Đăng xuất:
- Nhấp vào tên người dùng ở góc phải trên cùng
- Chọn "Đăng xuất" từ menu dropdown
- Xác nhận đăng xuất khi được hỏi
- Hệ thống sẽ tự động chuyển về trang chủ sau 5 giây

LƯU Ý QUAN TRỌNG:
- Mã sinh viên có thể chứa chữ cái và số, tối đa 20 ký tự (ví dụ: SV001, 2021001, ABC123)
- Mã sinh viên sẽ tự động chuyển thành chữ in hoa
- Ngày sinh không được vượt quá ngày hiện tại
- Ngày bắt đầu môn học không được trước ngày hiện tại
- Ngày kết thúc môn học phải sau ngày bắt đầu

2. Quản lý Sinh viên
-------------------
a) Xem danh sách sinh viên:
   - Truy cập http://127.0.0.1:8000/students/
   - Có thể tìm kiếm sinh viên theo mã, tên, email

b) Thêm sinh viên mới:
   - Nhấp vào nút "Thêm Sinh viên"
   - Điền đầy đủ thông tin và nhấp "Lưu"

c) Xem chi tiết sinh viên:
   - Nhấp vào tên sinh viên trong danh sách
   - Trang chi tiết hiển thị thông tin cá nhân và các môn học đã đăng ký

d) Chỉnh sửa thông tin sinh viên:
   - Trong trang chi tiết sinh viên, nhấp vào nút "Chỉnh sửa"
   - Cập nhật thông tin và nhấp "Lưu"

e) Xóa sinh viên:
   - Trong trang chi tiết sinh viên, nhấp vào nút "Xóa"
   - Xác nhận xóa

3. Quản lý Môn học
-----------------
a) Xem danh sách môn học:
   - Truy cập http://127.0.0.1:8000/courses/
   - Có thể tìm kiếm môn học theo mã, tên

b) Thêm môn học mới:
   - Nhấp vào nút "Thêm Môn học"
   - Điền đầy đủ thông tin và nhấp "Lưu"

c) Xem chi tiết môn học:
   - Nhấp vào tên môn học trong danh sách
   - Trang chi tiết hiển thị thông tin môn học và danh sách sinh viên đã đăng ký

d) Chỉnh sửa thông tin môn học:
   - Trong trang chi tiết môn học, nhấp vào nút "Chỉnh sửa"
   - Cập nhật thông tin và nhấp "Lưu"

e) Xóa môn học:
   - Trong trang chi tiết môn học, nhấp vào nút "Xóa"
   - Xác nhận xóa

4. Quản lý Đăng ký học
---------------------
a) Xem danh sách đăng ký:
   - Truy cập http://127.0.0.1:8000/courses/enrollments/
   - Có thể lọc theo sinh viên hoặc môn học

b) Thêm đăng ký mới:
   - Nhấp vào nút "Thêm Đăng ký"
   - Chọn sinh viên và môn học, sau đó nhấp "Lưu"

c) Cập nhật điểm:
   - Nhấp vào nút "Cập nhật" bên cạnh đăng ký
   - Nhập điểm chữ và điểm số, sau đó nhấp "Lưu"

d) Hủy đăng ký:
   - Nhấp vào nút "Hủy đăng ký" bên cạnh đăng ký
   - Xác nhận hủy

5. Xem Thống kê
--------------
- Truy cập http://127.0.0.1:8000/dashboard/ để xem thống kê
- Trang thống kê hiển thị:
  + Số lượng sinh viên (đang học/đã nghỉ)
  + Số lượng môn học (đang mở/đã đóng)
  + Tổng số đăng ký và điểm trung bình
  + Danh sách môn học phổ biến nhất

V. BẢO TRÌ HỆ THỐNG
------------------

1. Sao lưu cơ sở dữ liệu
------------------------
Để sao lưu dữ liệu, sử dụng lệnh:
   python manage.py dumpdata > backup.json

2. Khôi phục cơ sở dữ liệu
-------------------------
Để khôi phục dữ liệu từ bản sao lưu:
   python manage.py loaddata backup.json

3. Cập nhật hệ thống
------------------
Khi có phiên bản mới:
   - Kích hoạt môi trường ảo
   - Cập nhật mã nguồn (git pull hoặc tải về phiên bản mới)
   - Cập nhật các gói phụ thuộc: pip install -r requirements.txt
   - Cập nhật cơ sở dữ liệu: python manage.py migrate

VI. XỬ LÝ SỰ CỐ
--------------
1. Không thể đăng nhập:
   - Kiểm tra tên người dùng và mật khẩu
   - Đặt lại mật khẩu: python manage.py changepassword tên_người_dùng

2. Lỗi khi chạy máy chủ:
   - Kiểm tra logs để xác định lỗi
   - Đảm bảo môi trường ảo đã được kích hoạt
   - Kiểm tra các gói phụ thuộc: pip install -r requirements.txt

3. Lỗi khi tải lên hình ảnh:
   - Đảm bảo Pillow đã được cài đặt: pip install pillow
   - Kiểm tra quyền truy cập thư mục media

VII. THÔNG TIN LIÊN HỆ
---------------------
Nếu bạn gặp vấn đề hoặc có câu hỏi, vui lòng liên hệ:
- Email: <EMAIL>
- Website: https://example.com/support

=====================================================
© 2025 Hệ thống Quản lý Sinh viên
=====================================================
