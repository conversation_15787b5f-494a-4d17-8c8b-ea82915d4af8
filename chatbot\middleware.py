import json
import time
from django.http import JsonResponse
from django.core.cache import cache
from django.contrib.auth.models import AnonymousUser


class ChatbotSecurityMiddleware:
    """Middleware bảo mật cho chatbot"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Kiểm tra rate limiting cho chatbot API
        if request.path.startswith('/chatbot/api/'):
            if not self.check_rate_limit(request):
                return JsonResponse({
                    'success': False,
                    'error': 'Qu<PERSON> nhiều yêu cầu. Vui lòng thử lại sau.'
                }, status=429)
        
        response = self.get_response(request)
        return response
    
    def check_rate_limit(self, request):
        """Kiểm tra giới hạn số lượng request"""
        if isinstance(request.user, AnonymousUser):
            return False
        
        # Giới hạn 30 tin nhắn/phút cho mỗi user
        cache_key = f"chatbot_rate_limit_{request.user.id}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= 30:
            return False
        
        # Tăng counter và set expire time 60 giây
        cache.set(cache_key, current_count + 1, 60)
        return True


class ChatbotLoggingMiddleware:
    """Middleware logging cho chatbot"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        # Log chatbot API requests
        if request.path.startswith('/chatbot/api/'):
            duration = time.time() - start_time
            self.log_request(request, response, duration)
        
        return response
    
    def log_request(self, request, response, duration):
        """Log thông tin request"""
        import logging
        logger = logging.getLogger('chatbot')
        
        log_data = {
            'user': request.user.username if request.user.is_authenticated else 'anonymous',
            'path': request.path,
            'method': request.method,
            'status_code': response.status_code,
            'duration': round(duration, 3),
            'ip': self.get_client_ip(request)
        }
        
        logger.info(f"Chatbot API: {log_data}")
    
    def get_client_ip(self, request):
        """Lấy IP của client"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
