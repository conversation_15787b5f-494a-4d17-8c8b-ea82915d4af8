from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse_lazy


class AdminRequiredMixin(LoginRequiredMixin, UserPassesTestMixin):
    """
    Mixin để yêu cầu user phải là admin (is_staff=True).
    Tự động bao gồm LoginRequiredMixin.
    """
    
    def test_func(self):
        """Kiểm tra xem user có phải admin không."""
        return self.request.user.is_staff
    
    def handle_no_permission(self):
        """Xử lý khi user không có quyền."""
        if self.request.user.is_authenticated:
            messages.error(self.request, 'Bạn không có quyền truy cập vào trang này. Chỉ quản trị viên mới có thể thực hiện chức năng này.')
            return redirect('home')
        else:
            # Nếu chưa đăng nhập, chuyển đến trang login
            return super().handle_no_permission()


class StaffRequiredMixin(AdminRequiredMixin):
    """
    Mixin tương tự AdminRequiredMixin, dùng để kiểm tra is_staff.
    """
    pass


class StudentOnlyMixin(LoginRequiredMixin, UserPassesTestMixin):
    """
    Mixin để chỉ cho phép sinh viên (không phải admin) truy cập.
    """
    
    def test_func(self):
        """Kiểm tra xem user có phải sinh viên không (không phải staff)."""
        return not self.request.user.is_staff
    
    def handle_no_permission(self):
        """Xử lý khi user không có quyền."""
        if self.request.user.is_authenticated:
            messages.error(self.request, 'Trang này chỉ dành cho sinh viên.')
            return redirect('home')
        else:
            return super().handle_no_permission()
