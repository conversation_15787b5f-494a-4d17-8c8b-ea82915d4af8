{% extends 'base.html' %}

{% block title %}Đ<PERSON><PERSON> mật khẩu - <PERSON><PERSON> thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0"><i class="fas fa-key me-2"></i>Đ<PERSON><PERSON> mật khẩu</h4>
                </div>
                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Lỗi:</strong> Vui lòng kiểm tra lại thông tin.
                            {% if form.non_field_errors %}
                            <ul class="mb-0 mt-2">
                                {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="id_old_password" class="form-label">M<PERSON><PERSON> khẩu hiện tại</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" name="old_password" id="id_old_password" class="form-control {% if form.old_password.errors %}is-invalid{% endif %}" required>
                                {% if form.old_password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.old_password.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_new_password1" class="form-label">Mật khẩu mới</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="password" name="new_password1" id="id_new_password1" class="form-control {% if form.new_password1.errors %}is-invalid{% endif %}" required>
                                {% if form.new_password1.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.new_password1.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            {% if form.new_password1.help_text %}
                            <small class="form-text text-muted">
                                {{ form.new_password1.help_text|safe }}
                            </small>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="id_new_password2" class="form-label">Xác nhận mật khẩu mới</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="password" name="new_password2" id="id_new_password2" class="form-control {% if form.new_password2.errors %}is-invalid{% endif %}" required>
                                {% if form.new_password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.new_password2.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Đổi mật khẩu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
