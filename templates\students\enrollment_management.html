{% extends 'base.html' %}

{% block title %}{{ page_title }} - {{ student.full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'my-courses' %}">Môn học của tôi</a></li>
            <li class="breadcrumb-item active">{{ page_title }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-cog me-2"></i>{{ page_title }}</h1>
        <div class="text-muted">
            <i class="fas fa-user me-1"></i>{{ student.full_name }} ({{ student.student_id }})
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ total_enrollments }}</h3>
                    <p class="mb-0">Đã đăng ký</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ available_count }}</h3>
                    <p class="mb-0">Có thể đăng ký</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ total_enrollments|add:available_count }}</h3>
                    <p class="mb-0">Tổng môn học</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h3>{{ graded_enrollments }}</h3>
                    <p class="mb-0">Đã có điểm</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mb-4">
        <div class="btn-group" role="group">
            <a href="{% url 'my-courses' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Quay lại tổng quan
            </a>
            <a href="{% url 'my-courses-all' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i>Xem tất cả môn học
            </a>
        </div>
    </div>

    <!-- Enrolled Courses -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-clipboard-list me-2"></i>Môn học đã đăng ký
                <span class="badge bg-light text-dark ms-2">{{ enrollments.count }} môn</span>
            </h5>
        </div>
        <div class="card-body">
            {% if enrollments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>STT</th>
                                <th>Mã môn học</th>
                                <th>Tên môn học</th>
                                <th>Tín chỉ</th>
                                <th>Học kỳ</th>
                                <th>Năm học</th>
                                <th>Ngày đăng ký</th>
                                <th>Điểm</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for enrollment in enrollments %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td><strong>{{ enrollment.course.course_code }}</strong></td>
                                <td>
                                    <a href="{% url 'course-detail' enrollment.course.id %}" class="text-decoration-none">
                                        {{ enrollment.course.name }}
                                    </a>
                                </td>
                                <td class="text-center">{{ enrollment.course.credits }}</td>
                                <td>{{ enrollment.course.get_semester_display }}</td>
                                <td>{{ enrollment.course.year }}</td>
                                <td>{{ enrollment.enrollment_date|date:"d/m/Y" }}</td>
                                <td>
                                    {% if enrollment.grade %}
                                        {% if enrollment.grade == 'A+' or enrollment.grade == 'A' %}
                                            <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'B+' or enrollment.grade == 'B' %}
                                            <span class="badge bg-primary">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'C+' or enrollment.grade == 'C' %}
                                            <span class="badge bg-warning">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'D+' or enrollment.grade == 'D' %}
                                            <span class="badge bg-info">{{ enrollment.grade }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ enrollment.grade }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Chưa có</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if enrollment.course.is_active %}
                                        <span class="badge bg-success">Đang mở</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Đã đóng</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'course-detail' enrollment.course.id %}" class="btn btn-outline-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% comment %}
                                        <!-- Note: Hủy đăng ký chỉ nên được phép trong một số trường hợp nhất định -->
                                        {% if not enrollment.grade and enrollment.course.is_active %}
                                        <button class="btn btn-outline-danger" title="Hủy đăng ký" onclick="confirmUnenroll({{ enrollment.id }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        {% endcomment %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Chưa đăng ký môn học nào</h5>
                    <p class="text-muted">Bạn chưa đăng ký môn học nào.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Available Courses -->
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-plus-circle me-2"></i>Môn học có thể đăng ký
                <span class="badge bg-light text-dark ms-2">{{ available_courses.count }} môn</span>
            </h5>
        </div>
        <div class="card-body">
            {% if available_courses %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Lưu ý:</strong> Để đăng ký môn học mới, vui lòng liên hệ với quản trị viên hoặc phòng đào tạo.
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>STT</th>
                                <th>Mã môn học</th>
                                <th>Tên môn học</th>
                                <th>Tín chỉ</th>
                                <th>Học kỳ</th>
                                <th>Năm học</th>
                                <th>Thời gian</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for course in available_courses %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td><strong>{{ course.course_code }}</strong></td>
                                <td>
                                    <a href="{% url 'course-detail' course.id %}" class="text-decoration-none">
                                        {{ course.name }}
                                    </a>
                                </td>
                                <td class="text-center">{{ course.credits }}</td>
                                <td>{{ course.get_semester_display }}</td>
                                <td>{{ course.year }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ course.start_date|date:"d/m/Y" }} - {{ course.end_date|date:"d/m/Y" }}
                                    </small>
                                </td>
                                <td>
                                    {% if course.is_active %}
                                        <span class="badge bg-success">Đang mở</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Đã đóng</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'course-detail' course.id %}" class="btn btn-outline-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% comment %}
                                        <!-- Note: Chức năng đăng ký trực tiếp có thể được thêm sau -->
                                        {% if course.is_active %}
                                        <button class="btn btn-outline-success" title="Đăng ký" onclick="confirmEnroll({{ course.id }})">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        {% endif %}
                                        {% endcomment %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">Đã đăng ký tất cả môn học</h5>
                    <p class="text-muted">Bạn đã đăng ký tất cả các môn học hiện có trong hệ thống.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% comment %}
<!-- JavaScript for future enrollment/unenrollment functionality -->
<script>
function confirmEnroll(courseId) {
    if (confirm('Bạn có chắc chắn muốn đăng ký môn học này?')) {
        // TODO: Implement enrollment functionality
        alert('Chức năng đăng ký sẽ được triển khai sau. Vui lòng liên hệ quản trị viên.');
    }
}

function confirmUnenroll(enrollmentId) {
    if (confirm('Bạn có chắc chắn muốn hủy đăng ký môn học này?')) {
        // TODO: Implement unenrollment functionality
        alert('Chức năng hủy đăng ký sẽ được triển khai sau. Vui lòng liên hệ quản trị viên.');
    }
}
</script>
{% endcomment %}
{% endblock %}
