{% extends 'base.html' %}

{% block title %}{{ course.name }} - <PERSON><PERSON> thống Q<PERSON>ản lý Sinh viên{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'course-list' %}">Môn học</a></li>
            <li class="breadcrumb-item active">{{ course.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Course Info -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-book me-2"></i>Thông tin môn học</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px;">
                            <i class="fas fa-book fa-3x text-primary"></i>
                        </div>
                        <h4>{{ course.name }}</h4>
                        <p class="text-muted">Mã môn học: {{ course.course_code }}</p>
                        {% if course.is_active %}
                        <span class="badge bg-success">Đang mở</span>
                        {% else %}
                        <span class="badge bg-secondary">Đã đóng</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Thông tin chung</h6>
                        <hr>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Số tín chỉ:</div>
                            <div class="col-7">{{ course.credits }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Học kỳ:</div>
                            <div class="col-7">{{ course.get_semester_display }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Năm học:</div>
                            <div class="col-7">{{ course.year }}</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Thời gian</h6>
                        <hr>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Ngày bắt đầu:</div>
                            <div class="col-7">{{ course.start_date }}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-5 text-muted">Ngày kết thúc:</div>
                            <div class="col-7">{{ course.end_date }}</div>
                        </div>
                    </div>
                    
                    {% if course.description %}
                    <div>
                        <h6 class="text-primary">Mô tả</h6>
                        <hr>
                        <p>{{ course.description }}</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'course-list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại
                        </a>
                        {% if user.is_staff %}
                        <div>
                            <a href="{% url 'course-update' course.id %}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Chỉnh sửa
                            </a>
                            <a href="{% url 'course-delete' course.id %}" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Xóa
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Enrollments -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>Sinh viên đăng ký</h5>
                </div>
                <div class="card-body">
                    {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Mã SV</th>
                                    <th>Họ và tên</th>
                                    <th>Ngày đăng ký</th>
                                    <th>Điểm</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.student.student_id }}</td>
                                    <td>
                                        <a href="{% url 'student-detail' enrollment.student.id %}">
                                            {{ enrollment.student.full_name }}
                                        </a>
                                    </td>
                                    <td>{{ enrollment.enrollment_date }}</td>
                                    <td>
                                        {% if enrollment.grade %}
                                        <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% if enrollment.numeric_grade %}
                                        <small class="text-muted">({{ enrollment.numeric_grade }})</small>
                                        {% endif %}
                                        {% else %}
                                        <span class="badge bg-secondary">Chưa có điểm</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_authenticated %}
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'enrollment-update' enrollment.id %}" class="btn btn-warning" title="Cập nhật điểm">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'enrollment-delete' enrollment.id %}" class="btn btn-danger" title="Hủy đăng ký">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <p class="lead">Chưa có sinh viên nào đăng ký môn học này.</p>
                    </div>
                    {% endif %}
                </div>
                {% if user.is_authenticated %}
                <div class="card-footer text-end">
                    <a href="{% url 'enrollment-create' %}?course_id={{ course.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Thêm sinh viên
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
