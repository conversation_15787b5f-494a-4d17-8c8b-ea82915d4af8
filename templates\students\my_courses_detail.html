{% extends 'base.html' %}

{% block title %}{{ page_title }} - {{ student.full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'my-courses' %}">Môn học của tôi</a></li>
            <li class="breadcrumb-item active">{{ page_title }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-book-open me-2"></i>{{ page_title }}</h1>
        <div class="text-muted">
            <i class="fas fa-user me-1"></i>{{ student.full_name }} ({{ student.student_id }})
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ enrollments.count }}</h3>
                    <p class="mb-0">
                        {% if page_type == 'all' %}
                            Tổng môn học
                        {% elif page_type == 'graded' %}
                            Môn đã có điểm
                        {% elif page_type == 'ungraded' %}
                            Môn chưa có điểm
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        {% if page_type == 'graded' %}
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>
                        {% if enrollments %}
                            {{ enrollments|length|floatformat:0 }}
                        {% else %}
                            0
                        {% endif %}
                    </h3>
                    <p class="mb-0">Môn đã hoàn thành</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>
                        {% if avg_grade %}{{ avg_grade|floatformat:1 }}{% else %}--{% endif %}
                    </h3>
                    <p class="mb-0">Điểm trung bình</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="mb-4">
        <div class="btn-group" role="group">
            <a href="{% url 'my-courses' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Quay lại tổng quan
            </a>
            <a href="{% url 'my-courses-all' %}" class="btn {% if page_type == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                <i class="fas fa-list me-1"></i>Tất cả môn học
            </a>
            <a href="{% url 'my-courses-graded' %}" class="btn {% if page_type == 'graded' %}btn-success{% else %}btn-outline-success{% endif %}">
                <i class="fas fa-check-circle me-1"></i>Đã có điểm
            </a>
            <a href="{% url 'my-courses-ungraded' %}" class="btn {% if page_type == 'ungraded' %}btn-warning{% else %}btn-outline-warning{% endif %}">
                <i class="fas fa-clock me-1"></i>Chưa có điểm
            </a>
            <a href="{% url 'my-enrollment-management' %}" class="btn btn-outline-info">
                <i class="fas fa-cog me-1"></i>Quản lý đăng ký
            </a>
        </div>
    </div>

    <!-- Courses List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>{{ page_title }}
                <span class="badge bg-secondary ms-2">{{ enrollments.count }} môn</span>
            </h5>
        </div>
        <div class="card-body">
            {% if enrollments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>STT</th>
                                <th>Mã môn học</th>
                                <th>Tên môn học</th>
                                <th>Tín chỉ</th>
                                <th>Học kỳ</th>
                                <th>Năm học</th>
                                <th>Ngày đăng ký</th>
                                {% if page_type != 'ungraded' %}
                                <th>Điểm chữ</th>
                                <th>Điểm số</th>
                                {% endif %}
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for enrollment in enrollments %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td><strong>{{ enrollment.course.course_code }}</strong></td>
                                <td>
                                    <a href="{% url 'course-detail' enrollment.course.id %}" class="text-decoration-none">
                                        {{ enrollment.course.name }}
                                    </a>
                                </td>
                                <td class="text-center">{{ enrollment.course.credits }}</td>
                                <td>{{ enrollment.course.get_semester_display }}</td>
                                <td>{{ enrollment.course.year }}</td>
                                <td>{{ enrollment.enrollment_date|date:"d/m/Y" }}</td>
                                {% if page_type != 'ungraded' %}
                                <td>
                                    {% if enrollment.grade %}
                                        {% if enrollment.grade == 'A+' or enrollment.grade == 'A' %}
                                            <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'B+' or enrollment.grade == 'B' %}
                                            <span class="badge bg-primary">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'C+' or enrollment.grade == 'C' %}
                                            <span class="badge bg-warning">{{ enrollment.grade }}</span>
                                        {% elif enrollment.grade == 'D+' or enrollment.grade == 'D' %}
                                            <span class="badge bg-info">{{ enrollment.grade }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ enrollment.grade }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">--</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if enrollment.numeric_grade %}
                                        <strong>{{ enrollment.numeric_grade }}</strong>
                                    {% else %}
                                        <span class="text-muted">--</span>
                                    {% endif %}
                                </td>
                                {% endif %}
                                <td>
                                    {% if enrollment.grade %}
                                        <span class="badge bg-success">Đã có điểm</span>
                                    {% else %}
                                        <span class="badge bg-warning">Chưa có điểm</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'course-detail' enrollment.course.id %}" class="btn btn-outline-info" title="Xem chi tiết môn học">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Grade Legend -->
                {% if page_type == 'graded' %}
                <div class="mt-3">
                    <h6>Thang điểm:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-success">A+, A: Xuất sắc</span>
                        <span class="badge bg-primary">B+, B: Giỏi</span>
                        <span class="badge bg-warning">C+, C: Khá</span>
                        <span class="badge bg-info">D+, D: Trung bình</span>
                        <span class="badge bg-danger">F: Yếu</span>
                    </div>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-book fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">
                        {% if page_type == 'all' %}
                            Chưa có môn học nào
                        {% elif page_type == 'graded' %}
                            Chưa có môn học nào được chấm điểm
                        {% elif page_type == 'ungraded' %}
                            Tất cả môn học đã được chấm điểm
                        {% endif %}
                    </h4>
                    <p class="text-muted">
                        {% if page_type == 'all' %}
                            Bạn chưa đăng ký môn học nào. Vui lòng liên hệ với quản trị viên để đăng ký môn học.
                        {% elif page_type == 'graded' %}
                            Chưa có môn học nào được chấm điểm.
                        {% elif page_type == 'ungraded' %}
                            Tất cả các môn học đã đăng ký đều đã được chấm điểm.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
