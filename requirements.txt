# Core Django
Django>=5.2.0,<6.0.0
Pillow>=10.0.0

# Database (optional for production)
psycopg2-binary>=2.9.0

# Caching (optional for production)
django-redis>=5.3.0
redis>=4.5.0

# Security & Performance
django-cors-headers>=4.0.0
whitenoise>=6.5.0

# Chatbot dependencies (lightweight for production)
# Note: For production, consider using API-based NLP services
# transformers>=4.30.0
# torch>=2.0.0
# numpy>=1.24.0
# scikit-learn>=1.3.0
# sentence-transformers>=2.2.0
# underthesea>=6.0.0

# Development dependencies (install separately)
# pytest>=7.0.0
# pytest-django>=4.5.0
# black>=23.0.0
# flake8>=6.0.0
