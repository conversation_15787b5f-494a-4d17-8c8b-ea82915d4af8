{% extends 'base.html' %}

{% block title %}
    {% if form.instance.pk %}Chỉnh sửa{% else %}Thêm mới{% endif %} Môn học - <PERSON>ệ thống Quản lý Sinh viên
{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="{% url 'course-list' %}">Môn học</a></li>
            <li class="breadcrumb-item active">
                {% if form.instance.pk %}Chỉnh sửa{% else %}Thêm mới{% endif %} Môn học
            </li>
        </ol>
    </nav>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-{% if form.instance.pk %}edit{% else %}plus{% endif %} me-2"></i>
                {% if form.instance.pk %}Chỉnh sửa{% else %}Thêm mới{% endif %} Môn học
            </h5>
        </div>
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}

                {% if form.errors %}
                <div class="alert alert-danger">
                    <strong>Lỗi:</strong> Vui lòng kiểm tra lại thông tin.
                </div>
                {% endif %}

                <div class="row">
                    <!-- Thông tin cơ bản -->
                    <div class="col-md-6">
                        <h5 class="mb-3 text-primary">Thông tin cơ bản</h5>

                        <div class="mb-3">
                            <label for="{{ form.course_code.id_for_label }}" class="form-label">{{ form.course_code.label }}</label>
                            <input type="text" name="{{ form.course_code.name }}" id="{{ form.course_code.id_for_label }}"
                                   class="form-control {% if form.course_code.errors %}is-invalid{% endif %}"
                                   value="{{ form.course_code.value|default:'' }}" required>
                            {% if form.course_code.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.course_code.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            <input type="text" name="{{ form.name.name }}" id="{{ form.name.id_for_label }}"
                                   class="form-control {% if form.name.errors %}is-invalid{% endif %}"
                                   value="{{ form.name.value|default:'' }}" required>
                            {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.credits.id_for_label }}" class="form-label">{{ form.credits.label }}</label>
                            <input type="number" name="{{ form.credits.name }}" id="{{ form.credits.id_for_label }}"
                                   class="form-control {% if form.credits.errors %}is-invalid{% endif %}"
                                   value="{{ form.credits.value|default:'' }}" min="1" max="10" required>
                            {% if form.credits.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.credits.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            <textarea name="{{ form.description.name }}" id="{{ form.description.id_for_label }}"
                                      class="form-control {% if form.description.errors %}is-invalid{% endif %}"
                                      rows="4">{{ form.description.value|default:'' }}</textarea>
                            {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Thời gian và trạng thái -->
                    <div class="col-md-6">
                        <h5 class="mb-3 text-primary">Thời gian</h5>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.semester.id_for_label }}" class="form-label">{{ form.semester.label }}</label>
                                <select name="{{ form.semester.name }}" id="{{ form.semester.id_for_label }}"
                                        class="form-select {% if form.semester.errors %}is-invalid{% endif %}" required>
                                    <option value="" {% if not form.semester.value %}selected{% endif %}>-- Chọn học kỳ --</option>
                                    {% for choice in form.semester.field.choices %}
                                    <option value="{{ choice.0 }}" {% if form.semester.value == choice.0|stringformat:"s" %}selected{% endif %}>
                                        {{ choice.1 }}
                                    </option>
                                    {% endfor %}
                                </select>
                                {% if form.semester.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.semester.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.year.id_for_label }}" class="form-label">{{ form.year.label }}</label>
                                <input type="number" name="{{ form.year.name }}" id="{{ form.year.id_for_label }}"
                                       class="form-control {% if form.year.errors %}is-invalid{% endif %}"
                                       value="{{ form.year.value|default:'' }}" min="2000" max="2100" required>
                                {% if form.year.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.year.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                                <div class="date-input-wrapper">
                                    <input type="date" name="{{ form.start_date.name }}" id="{{ form.start_date.id_for_label }}"
                                           class="form-control {% if form.start_date.errors %}is-invalid{% endif %}"
                                           value="{{ form.start_date.value|date:'Y-m-d'|default:'' }}"
                                           min="{% now 'Y-m-d' %}"
                                           required>
                                </div>
                                {% if form.start_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">Ngày bắt đầu môn học</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                                <div class="date-input-wrapper">
                                    <input type="date" name="{{ form.end_date.name }}" id="{{ form.end_date.id_for_label }}"
                                           class="form-control {% if form.end_date.errors %}is-invalid{% endif %}"
                                           value="{{ form.end_date.value|date:'Y-m-d'|default:'' }}"
                                           min="{% now 'Y-m-d' %}"
                                           required>
                                </div>
                                {% if form.end_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">Ngày kết thúc môn học</small>
                            </div>
                        </div>

                        <h5 class="mb-3 mt-4 text-primary">Trạng thái</h5>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="{{ form.is_active.name }}"
                                       id="{{ form.is_active.id_for_label }}"
                                       {% if form.is_active.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                            <div class="text-danger">
                                {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{% if form.instance.pk %}{% url 'course-detail' form.instance.pk %}{% else %}{% url 'course-list' %}{% endif %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Hủy
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Lưu
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
