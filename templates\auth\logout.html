{% extends 'base.html' %}

{% block title %}Đ<PERSON>ng xuất - <PERSON><PERSON> thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="card shadow">
                <div class="card-body p-5">
                    <i class="fas fa-check-circle text-success fa-5x mb-3"></i>
                    <h2 class="mb-3">Đăng xuất thành công</h2>
                    <p class="lead mb-4">Bạn đã đăng xuất khỏi hệ thống an toàn.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Trang sẽ tự động chuyển về trang chủ sau <span id="countdown">5</span> giây...
                    </div>
                    <a href="{% url 'login' %}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i><PERSON><PERSON>ng nhập lại
                    </a>
                    <a href="{% url 'home' %}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-home me-2"></i>Về trang chủ ngay
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Countdown and auto redirect
var countdown = 5;
var countdownElement = document.getElementById('countdown');

var timer = setInterval(function() {
    countdown--;
    if (countdownElement) {
        countdownElement.textContent = countdown;
    }

    if (countdown <= 0) {
        clearInterval(timer);
        window.location.href = "{% url 'home' %}";
    }
}, 1000);
</script>
{% endblock %}
