from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class FAQ(models.Model):
    """Model lưu trữ câu hỏi thường gặp và câu trả lời"""
    CATEGORY_CHOICES = [
        ('general', 'Thông tin chung'),
        ('student', 'Thông tin sinh viên'),
        ('course', '<PERSON>ôn học'),
        ('enrollment', 'Đăng ký học'),
        ('grade', 'Điểm số'),
        ('system', '<PERSON><PERSON> thống'),
    ]

    question = models.TextField(verbose_name="Câu hỏi")
    answer = models.TextField(verbose_name="Câu trả lời")
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='general',
        verbose_name="Danh mục"
    )
    keywords = models.TextField(
        blank=True,
        null=True,
        help_text="Từ khóa liên quan, cách nhau bởi dấu phẩy",
        verbose_name="Từ khóa"
    )
    is_active = models.BooleanField(default=True, verbose_name="<PERSON><PERSON><PERSON> hoạt")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Ngày cập nhật")

    class Meta:
        verbose_name = "Câu hỏi thường gặp"
        verbose_name_plural = "Câu hỏi thường gặp"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.question[:50]}..."


class ChatSession(models.Model):
    """Model lưu trữ phiên chat của người dùng"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='chat_sessions',
        verbose_name="Người dùng"
    )
    session_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="ID phiên"
    )
    started_at = models.DateTimeField(auto_now_add=True, verbose_name="Bắt đầu")
    last_activity = models.DateTimeField(auto_now=True, verbose_name="Hoạt động cuối")
    is_active = models.BooleanField(default=True, verbose_name="Đang hoạt động")

    class Meta:
        verbose_name = "Phiên chat"
        verbose_name_plural = "Phiên chat"
        ordering = ['-last_activity']

    def __str__(self):
        return f"Chat {self.user.username} - {self.session_id[:8]}"


class ChatHistory(models.Model):
    """Model lưu trữ lịch sử tin nhắn chat"""
    MESSAGE_TYPE_CHOICES = [
        ('user', 'Người dùng'),
        ('bot', 'Bot'),
    ]

    session = models.ForeignKey(
        ChatSession,
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name="Phiên chat"
    )
    message_type = models.CharField(
        max_length=10,
        choices=MESSAGE_TYPE_CHOICES,
        verbose_name="Loại tin nhắn"
    )
    message = models.TextField(verbose_name="Tin nhắn")
    intent = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="Ý định"
    )
    confidence = models.FloatField(
        blank=True,
        null=True,
        verbose_name="Độ tin cậy"
    )
    response_time = models.FloatField(
        blank=True,
        null=True,
        help_text="Thời gian phản hồi (giây)",
        verbose_name="Thời gian phản hồi"
    )
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name="Thời gian")

    class Meta:
        verbose_name = "Lịch sử chat"
        verbose_name_plural = "Lịch sử chat"
        ordering = ['timestamp']

    def __str__(self):
        return f"{self.session.user.username} - {self.message_type} - {self.timestamp}"
