<!DOCTYPE html>
{% load static %}
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Hệ thống Quản lý <PERSON> viên{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-graduation-cap me-2"></i>Quản lý Sinh viên
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'home' %}">
                            <i class="fas fa-home me-1"></i>Trang chủ
                        </a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link {% if '/students/' in request.path %}active{% endif %}" href="{% url 'student-list' %}">
                            <i class="fas fa-user-graduate me-1"></i>Sinh viên
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/courses/' in request.path and not '/enrollments/' in request.path %}active{% endif %}" href="{% url 'course-list' %}">
                            <i class="fas fa-book me-1"></i>Môn học
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/enrollments/' in request.path %}active{% endif %}" href="{% url 'enrollment-list' %}">
                            <i class="fas fa-clipboard-list me-1"></i>Đăng ký học
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/dashboard/' in request.path %}active{% endif %}" href="{% url 'dashboard' %}">
                            <i class="fas fa-chart-line me-1"></i>Thống kê
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/reports/' in request.path %}active{% endif %}" href="{% url 'reports' %}">
                            <i class="fas fa-file-alt me-1"></i>Báo cáo
                        </a>
                    </li>
                    {% elif user.is_authenticated %}
                    <!-- Menu dành cho sinh viên -->
                    <li class="nav-item">
                        <a class="nav-link {% if '/student-profile/' in request.path %}active{% endif %}" href="{% url 'student-profile' %}">
                            <i class="fas fa-user me-1"></i>Thông tin cá nhân
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/my-courses/' in request.path %}active{% endif %}" href="{% url 'my-courses' %}">
                            <i class="fas fa-book-open me-1"></i>Môn học của tôi
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% if user.is_staff %}
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}"><i class="fas fa-cog me-1"></i>Quản trị</a></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'password_change' %}"><i class="fas fa-key me-1"></i>Đổi mật khẩu</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'logout' %}" style="display: inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item" style="border: none; background: none; width: 100%; text-align: left;">
                                        <i class="fas fa-sign-out-alt me-1"></i>Đăng xuất
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}"><i class="fas fa-sign-in-alt me-1"></i>Đăng nhập</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'register' %}"><i class="fas fa-user-plus me-1"></i>Đăng ký</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Messages -->
            {% if messages %}
            <div class="col-12 mt-3">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Main Content -->
            <main class="col-12">
                <div class="content">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; {% now "Y" %} Hệ thống Quản lý Sinh viên</p>
        </div>
    </footer>

    <!-- Chatbot Widget (only for authenticated users) -->
    {% if user.is_authenticated %}
        {% include 'chatbot/widget.html' %}
    {% endif %}

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
