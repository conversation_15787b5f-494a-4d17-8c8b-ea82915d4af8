from django.contrib import admin
from .models import Student

@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ('student_id', 'full_name', 'email', 'phone_number', 'date_of_birth', 'is_active')
    list_filter = ('is_active', 'gender', 'enrollment_date')
    search_fields = ('student_id', 'first_name', 'last_name', 'email', 'phone_number')
    fieldsets = (
        ('Thông tin cá nhân', {
            'fields': ('student_id', 'first_name', 'last_name', 'date_of_birth', 'gender', 'photo')
        }),
        ('Thông tin liên hệ', {
            'fields': ('email', 'phone_number', 'address')
        }),
        ('Thông tin học tập', {
            'fields': ('enrollment_date', 'is_active')
        }),
    )
    readonly_fields = ('enrollment_date',)
