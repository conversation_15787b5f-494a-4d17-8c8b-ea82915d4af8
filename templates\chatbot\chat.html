{% extends 'base.html' %}
{% load static %}

{% block title %}Chat<PERSON> - <PERSON><PERSON> thống Quản lý Sinh viên{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        Trợ lý ảo - <PERSON>ệ thống Quản lý Sinh viên
                    </h5>
                </div>
                <div class="card-body">
                    <div id="chat-container" style="height: 400px; overflow-y: auto; border: 1px solid #dee2e6; padding: 15px; background-color: #f8f9fa;">
                        <div class="message bot-message mb-3">
                            <div class="d-flex">
                                <div class="me-2">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="bg-white p-2 rounded shadow-sm">
                                        Xin chào! Tôi là trợ lý ảo của hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?
                                    </div>
                                    <small class="text-muted">Vừa xong</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <div class="input-group">
                            <input type="text" id="message-input" class="form-control" placeholder="Nhập tin nhắn..." maxlength="500">
                            <button id="send-button" class="btn btn-primary" type="button">
                                <i class="fas fa-paper-plane"></i>
                                Gửi
                            </button>
                        </div>
                        <div id="typing-indicator" class="mt-2" style="display: none;">
                            <small class="text-muted">
                                <i class="fas fa-circle-notch fa-spin"></i>
                                Đang trả lời...
                            </small>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button id="clear-chat" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-trash"></i>
                            Xóa lịch sử
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6>Gợi ý câu hỏi:</h6>
                <div class="d-flex flex-wrap gap-2">
                    <button class="btn btn-outline-primary btn-sm suggestion-btn">Thông tin sinh viên của tôi</button>
                    <button class="btn btn-outline-primary btn-sm suggestion-btn">Danh sách môn đã đăng ký</button>
                    <button class="btn btn-outline-primary btn-sm suggestion-btn">Điểm số của tôi</button>
                    <button class="btn btn-outline-primary btn-sm suggestion-btn">Môn học IT001</button>
                    <button class="btn btn-outline-primary btn-sm suggestion-btn">Hướng dẫn sử dụng</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatContainer = document.getElementById('chat-container');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const clearButton = document.getElementById('clear-chat');
    const typingIndicator = document.getElementById('typing-indicator');
    const suggestionButtons = document.querySelectorAll('.suggestion-btn');
    
    // Suggestion buttons
    suggestionButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            messageInput.value = this.textContent;
            sendMessage();
        });
    });
    
    // Send message
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    // Clear chat
    clearButton.addEventListener('click', function() {
        if (confirm('Bạn có chắc muốn xóa lịch sử chat?')) {
            clearChat();
        }
    });
    
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;
        
        // Add user message
        addMessage('user', message);
        messageInput.value = '';
        
        // Show typing
        showTyping(true);
        
        // Send to server
        fetch('/chatbot/api/message/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            showTyping(false);
            if (data.success) {
                addMessage('bot', data.response);
            } else {
                addMessage('bot', 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.');
            }
        })
        .catch(error => {
            showTyping(false);
            addMessage('bot', 'Không thể kết nối đến server. Vui lòng thử lại.');
        });
    }
    
    function addMessage(type, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message mb-3`;
        
        const isUser = type === 'user';
        const flexClass = isUser ? 'justify-content-end' : '';
        const bgColor = isUser ? 'bg-primary text-white' : 'bg-white';
        const icon = isUser ? 'fas fa-user' : 'fas fa-robot';
        const avatarBg = isUser ? 'bg-success' : 'bg-primary';
        
        messageDiv.innerHTML = `
            <div class="d-flex ${flexClass}">
                ${!isUser ? `
                <div class="me-2">
                    <div class="${avatarBg} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                        <i class="${icon}"></i>
                    </div>
                </div>
                ` : ''}
                <div class="flex-grow-1" style="max-width: 80%;">
                    <div class="${bgColor} p-2 rounded shadow-sm" style="white-space: pre-wrap;">${text}</div>
                    <small class="text-muted">Vừa xong</small>
                </div>
                ${isUser ? `
                <div class="ms-2">
                    <div class="${avatarBg} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                        <i class="${icon}"></i>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    function showTyping(show) {
        typingIndicator.style.display = show ? 'block' : 'none';
    }
    
    function clearChat() {
        fetch('/chatbot/api/clear/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                chatContainer.innerHTML = `
                    <div class="message bot-message mb-3">
                        <div class="d-flex">
                            <div class="me-2">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                    <i class="fas fa-robot"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="bg-white p-2 rounded shadow-sm">
                                    Xin chào! Tôi là trợ lý ảo của hệ thống Quản lý Sinh viên. Tôi có thể giúp bạn tìm hiểu về thông tin sinh viên, môn học, đăng ký học và nhiều thông tin khác. Bạn cần hỗ trợ gì?
                                </div>
                                <small class="text-muted">Vừa xong</small>
                            </div>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error clearing chat:', error);
        });
    }
    
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Load chat history on page load
    loadChatHistory();
    
    function loadChatHistory() {
        fetch('/chatbot/api/history/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.messages.length > 0) {
                chatContainer.innerHTML = '';
                data.messages.forEach(msg => {
                    addMessage(msg.type, msg.message);
                });
            }
        })
        .catch(error => {
            console.error('Error loading chat history:', error);
        });
    }
});
</script>
{% endblock %}
