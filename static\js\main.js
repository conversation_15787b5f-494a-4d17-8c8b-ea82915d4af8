// Main JavaScript file for Student Management System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Add active class to current nav item
    var currentLocation = window.location.pathname;
    var navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    navLinks.forEach(function(link) {
        var linkPath = link.getAttribute('href');
        if (currentLocation === linkPath ||
            (linkPath !== '/' && currentLocation.startsWith(linkPath))) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });

    // Form validation
    var forms = document.querySelectorAll('.needs-validation');

    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Student ID validation
    var studentIdInputs = document.querySelectorAll('input[name="student_id"]');

    studentIdInputs.forEach(function(input) {
        // Initialize character counter
        updateCharacterCounter(input);

        input.addEventListener('input', function() {
            var value = this.value;
            var isValid = /^[A-Za-z0-9]*$/.test(value);

            // Update character counter
            updateCharacterCounter(this);

            if (!isValid && value.length > 0) {
                this.setCustomValidity('Mã sinh viên chỉ được chứa chữ cái và số');
                this.classList.add('is-invalid');
            } else if (value.length > 20) {
                this.setCustomValidity('Mã sinh viên không được vượt quá 20 ký tự');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }

            // Convert to uppercase for consistency
            var cursorPosition = this.selectionStart;
            this.value = this.value.toUpperCase();
            this.setSelectionRange(cursorPosition, cursorPosition);
        });

        input.addEventListener('blur', function() {
            if (this.value.length === 0) {
                this.setCustomValidity('Mã sinh viên là bắt buộc');
                this.classList.add('is-invalid');
            }
        });
    });

    // Function to update character counter
    function updateCharacterCounter(input) {
        var charCountElement = document.getElementById('char-count');
        var counterElement = document.getElementById('student-id-counter');

        if (charCountElement && counterElement) {
            var length = input.value.length;
            charCountElement.textContent = length;

            // Update counter color based on length
            counterElement.classList.remove('warning', 'danger');
            if (length >= 18) {
                counterElement.classList.add('danger');
            } else if (length >= 15) {
                counterElement.classList.add('warning');
            }
        }
    }

    // Print functionality
    var printButtons = document.querySelectorAll('.btn-print');

    printButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });
    });

    // Search input focus
    var searchInputs = document.querySelectorAll('.search-input');

    searchInputs.forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('search-focused');
        });

        input.addEventListener('blur', function() {
            if (this.value === '') {
                this.parentElement.classList.remove('search-focused');
            }
        });
    });

    // Confirm delete
    var deleteButtons = document.querySelectorAll('.btn-delete-confirm');

    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Bạn có chắc chắn muốn xóa mục này?')) {
                e.preventDefault();
            }
        });
    });

    // Logout confirmation
    var logoutForms = document.querySelectorAll('form[action*="logout"]');

    logoutForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            if (!confirm('Bạn có chắc chắn muốn đăng xuất?')) {
                e.preventDefault();
            }
        });
    });

    // Grade calculator
    var gradeInputs = document.querySelectorAll('.grade-select');
    var numericGradeInputs = document.querySelectorAll('.numeric-grade-input');

    // Grade conversion map
    const gradeMap = {
        'A+': 4.0,
        'A': 3.7,
        'B+': 3.3,
        'B': 3.0,
        'C+': 2.7,
        'C': 2.3,
        'D+': 2.0,
        'D': 1.7,
        'F': 0.0
    };

    // Convert letter grade to numeric
    gradeInputs.forEach(function(select) {
        select.addEventListener('change', function() {
            var numericInput = document.getElementById(this.dataset.numericTarget);
            if (numericInput && this.value) {
                numericInput.value = gradeMap[this.value] || '';
            }
        });
    });

    // Convert numeric grade to letter
    numericGradeInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            var gradeSelect = document.getElementById(this.dataset.gradeTarget);
            if (gradeSelect) {
                var value = parseFloat(this.value);
                var grade = '';

                if (value >= 3.7) grade = 'A+';
                else if (value >= 3.3) grade = 'A';
                else if (value >= 3.0) grade = 'B+';
                else if (value >= 2.7) grade = 'B';
                else if (value >= 2.3) grade = 'C+';
                else if (value >= 2.0) grade = 'C';
                else if (value >= 1.7) grade = 'D+';
                else if (value >= 1.0) grade = 'D';
                else if (value >= 0) grade = 'F';

                gradeSelect.value = grade;
            }
        });
    });

    // Date input enhancements
    var dateInputs = document.querySelectorAll('input[type="date"]');

    dateInputs.forEach(function(input) {
        // Set default placeholder
        if (!input.value) {
            input.style.color = '#6c757d';
        }

        // Handle focus and blur events
        input.addEventListener('focus', function() {
            this.style.color = '#212529';
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.style.color = '#6c757d';
            }
        });

        input.addEventListener('change', function() {
            this.style.color = '#212529';

            // Validate date range for course dates
            if (this.id.includes('start_date')) {
                var endDateInput = document.getElementById(this.id.replace('start_date', 'end_date'));
                if (endDateInput && endDateInput.value && this.value > endDateInput.value) {
                    alert('Ngày bắt đầu không thể sau ngày kết thúc!');
                    this.value = '';
                }
            } else if (this.id.includes('end_date')) {
                var startDateInput = document.getElementById(this.id.replace('end_date', 'start_date'));
                if (startDateInput && startDateInput.value && this.value < startDateInput.value) {
                    alert('Ngày kết thúc không thể trước ngày bắt đầu!');
                    this.value = '';
                }
            }
        });
    });

    // Clickable cards enhancement
    var clickableCards = document.querySelectorAll('.clickable-card');

    clickableCards.forEach(function(card) {
        // Add ripple effect on click
        card.addEventListener('click', function(e) {
            var ripple = document.createElement('div');
            ripple.classList.add('ripple');

            var rect = this.getBoundingClientRect();
            var size = Math.max(rect.width, rect.height);
            var x = e.clientX - rect.left - size / 2;
            var y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            this.appendChild(ripple);

            setTimeout(function() {
                ripple.remove();
            }, 600);
        });

        // Add keyboard navigation
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });

        // Make cards focusable
        if (!card.hasAttribute('tabindex')) {
            card.setAttribute('tabindex', '0');
        }
    });

    // Add loading states for navigation
    var navLinks = document.querySelectorAll('a[href*="my-courses"]');

    navLinks.forEach(function(link) {
        link.addEventListener('click', function() {
            var icon = this.querySelector('i');
            if (icon) {
                var originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                setTimeout(function() {
                    icon.className = originalClass;
                }, 1000);
            }
        });
    });
});

// Function to filter table rows
function filterTable(inputId, tableId) {
    var input = document.getElementById(inputId);
    var filter = input.value.toUpperCase();
    var table = document.getElementById(tableId);
    var tr = table.getElementsByTagName("tr");
    var noResults = document.getElementById(tableId + "-no-results");
    var found = false;

    for (var i = 0; i < tr.length; i++) {
        var td = tr[i].getElementsByTagName("td");
        var display = false;

        for (var j = 0; j < td.length; j++) {
            if (td[j]) {
                var txtValue = td[j].textContent || td[j].innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    display = true;
                    found = true;
                    break;
                }
            }
        }

        if (tr[i].getElementsByTagName("th").length > 0) {
            // This is a header row, always show it
            tr[i].style.display = "";
        } else {
            tr[i].style.display = display ? "" : "none";
        }
    }

    if (noResults) {
        noResults.style.display = found ? "none" : "block";
    }
}
